<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Question 7: Cryptographic Hash Functions in Blockchain Security</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            line-height: 1.6;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .section {
            margin: 30px 0;
            padding: 25px;
            border-radius: 12px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-left: 5px solid #667eea;
        }
        .diagram-container {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        .concept-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #2196f3;
        }
        .example-box {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #4caf50;
        }
        .advantages-box {
            background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 30%);
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #ff9800;
        }
        .disadvantages-box {
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #f44336;
        }
        .highlight {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            padding: 3px 8px;
            border-radius: 4px;
            font-weight: bold;
            color: #856404;
        }
        .code-snippet {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        th {
            background: #343a40;
            color: white;
            font-weight: bold;
        }
        tr:hover {
            background: #f8f9fa;
        }
        .emoji {
            font-size: 1.2em;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Question 7: Cryptographic Hash Functions in Blockchain Security</h1>

        <div class="section">
            <h2><span class="emoji">📚</span>What are Cryptographic Hash Functions?</h2>

            <div class="concept-box">
                <h3>Definition and Core Properties</h3>
                <p>A <span class="highlight">cryptographic hash function</span> is a mathematical algorithm that takes input data of any size and produces a fixed-size string of characters (hash). It's a one-way function—easy to compute forward but computationally infeasible to reverse.</p>

                <p><strong>Essential Properties:</strong></p>
                <ul>
                    <li><strong>Deterministic:</strong> Same input always produces same output</li>
                    <li><strong>Fixed Output Size:</strong> Always produces same length hash regardless of input size</li>
                    <li><strong>Avalanche Effect:</strong> Small input change drastically changes output</li>
                    <li><strong>Pre-image Resistance:</strong> Cannot find input from hash (one-way)</li>
                    <li><strong>Second Pre-image Resistance:</strong> Cannot find different input with same hash</li>
                    <li><strong>Collision Resistance:</strong> Hard to find two inputs with same hash</li>
                </ul>
            </div>

            <div class="diagram-container">
                <svg width="100%" height="700" viewBox="0 0 1200 700">
                    <!-- Title -->
                    <text x="600" y="30" text-anchor="middle" font-size="18" font-weight="bold">CRYPTOGRAPHIC HASH FUNCTION OPERATION</text>

                    <!-- Hash Function Process -->
                    <g>
                        <!-- Input -->
                        <rect x="100" y="80" width="200" height="60" fill="#E3F2FD" stroke="#2196F3" stroke-width="2"/>
                        <text x="200" y="105" text-anchor="middle" font-size="14" font-weight="bold">INPUT DATA</text>
                        <text x="200" y="125" text-anchor="middle" font-size="10">(Any size: bytes to terabytes)</text>

                        <!-- Hash Function -->
                        <rect x="400" y="80" width="200" height="60" fill="#FFF3E0" stroke="#FF9800" stroke-width="3"/>
                        <text x="500" y="105" text-anchor="middle" font-size="14" font-weight="bold">HASH FUNCTION</text>
                        <text x="500" y="125" text-anchor="middle" font-size="10">(SHA-256, Keccak, etc.)</text>

                        <!-- Output -->
                        <rect x="700" y="80" width="200" height="60" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2"/>
                        <text x="800" y="105" text-anchor="middle" font-size="14" font-weight="bold">HASH OUTPUT</text>
                        <text x="800" y="125" text-anchor="middle" font-size="10">(Fixed size: 256 bits)</text>

                        <!-- Arrows -->
                        <path d="M 300 110 L 400 110" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                        <path d="M 600 110 L 700 110" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                    </g>

                    <!-- Examples -->
                    <text x="600" y="190" text-anchor="middle" font-size="16" font-weight="bold">HASH FUNCTION EXAMPLES</text>

                    <!-- Example 1 -->
                    <g>
                        <rect x="50" y="210" width="300" height="40" fill="#BBDEFB" stroke="#2196F3" stroke-width="1"/>
                        <text x="200" y="235" text-anchor="middle" font-size="12">"Hello World"</text>

                        <text x="400" y="235" text-anchor="middle" font-size="14">SHA-256</text>

                        <rect x="450" y="210" width="700" height="40" fill="#C8E6C9" stroke="#4CAF50" stroke-width="1"/>
                        <text x="800" y="235" text-anchor="middle" font-size="10">a591a6d40bf420404a011733cfb7b190d62c65bf0bcda32b57b277d9ad9f146e</text>

                        <path d="M 350 230 L 450 230" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                    </g>

                    <!-- Example 2 -->
                    <g>
                        <rect x="50" y="270" width="300" height="40" fill="#BBDEFB" stroke="#2196F3" stroke-width="1"/>
                        <text x="200" y="295" text-anchor="middle" font-size="12">"Hello World!"</text>

                        <text x="400" y="295" text-anchor="middle" font-size="14">SHA-256</text>

                        <rect x="450" y="270" width="700" height="40" fill="#C8E6C9" stroke="#4CAF50" stroke-width="1"/>
                        <text x="800" y="295" text-anchor="middle" font-size="10">7f83b1657ff1fc53b92dc18148a1d65dfc2d4b1fa3d677284addd200126d9069</text>

                        <path d="M 350 290 L 450 290" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                    </g>

                    <text x="600" y="340" text-anchor="middle" font-size="12" fill="#F44336" font-weight="bold">
                        Notice: One character change completely changes the hash (Avalanche Effect)
                    </text>

                    <!-- Hash Properties Visualization -->
                    <text x="600" y="380" text-anchor="middle" font-size="16" font-weight="bold">KEY PROPERTIES DEMONSTRATION</text>

                    <!-- Deterministic -->
                    <g>
                        <rect x="100" y="400" width="200" height="80" fill="#E1BEE7" stroke="#9C27B0" stroke-width="2"/>
                        <text x="200" y="425" text-anchor="middle" font-size="12" font-weight="bold">DETERMINISTIC</text>
                        <text x="200" y="445" text-anchor="middle" font-size="10">Same input →</text>
                        <text x="200" y="460" text-anchor="middle" font-size="10">Same output</text>
                        <text x="200" y="475" text-anchor="middle" font-size="10">Always!</text>
                    </g>

                    <!-- Fixed Size -->
                    <g>
                        <rect x="350" y="400" width="200" height="80" fill="#FFCDD2" stroke="#F44336" stroke-width="2"/>
                        <text x="450" y="425" text-anchor="middle" font-size="12" font-weight="bold">FIXED SIZE</text>
                        <text x="450" y="445" text-anchor="middle" font-size="10">1 byte input →</text>
                        <text x="450" y="460" text-anchor="middle" font-size="10">256-bit hash</text>
                        <text x="450" y="475" text-anchor="middle" font-size="10">1 TB input → 256-bit hash</text>
                    </g>

                    <!-- One-Way -->
                    <g>
                        <rect x="600" y="400" width="200" height="80" fill="#FFF3E0" stroke="#FF9800" stroke-width="2"/>
                        <text x="700" y="425" text-anchor="middle" font-size="12" font-weight="bold">ONE-WAY</text>
                        <text x="700" y="445" text-anchor="middle" font-size="10">Easy: Input → Hash</text>
                        <text x="700" y="460" text-anchor="middle" font-size="10">Impossible:</text>
                        <text x="700" y="475" text-anchor="middle" font-size="10">Hash → Input</text>
                    </g>

                    <!-- Collision Resistant -->
                    <g>
                        <rect x="850" y="400" width="200" height="80" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2"/>
                        <text x="950" y="425" text-anchor="middle" font-size="12" font-weight="bold">COLLISION</text>
                        <text x="950" y="440" text-anchor="middle" font-size="12" font-weight="bold">RESISTANT</text>
                        <text x="950" y="460" text-anchor="middle" font-size="10">Nearly impossible to</text>
                        <text x="950" y="475" text-anchor="middle" font-size="10">find two inputs with</text>
                        <text x="950" y="490" text-anchor="middle" font-size="10">same hash</text>
                    </g>

                    <!-- Blockchain Applications -->
                    <text x="600" y="530" text-anchor="middle" font-size="16" font-weight="bold">BLOCKCHAIN APPLICATIONS</text>

                    <!-- Block Hashing -->
                    <g>
                        <rect x="100" y="550" width="150" height="60" fill="#BBDEFB" stroke="#2196F3" stroke-width="2"/>
                        <text x="175" y="575" text-anchor="middle" font-size="12" font-weight="bold">BLOCK HASHING</text>
                        <text x="175" y="590" text-anchor="middle" font-size="10">Each block has</text>
                        <text x="175" y="605" text-anchor="middle" font-size="10">unique hash ID</text>
                    </g>

                    <!-- Chain Linking -->
                    <g>
                        <rect x="300" y="550" width="150" height="60" fill="#C8E6C9" stroke="#4CAF50" stroke-width="2"/>
                        <text x="375" y="575" text-anchor="middle" font-size="12" font-weight="bold">CHAIN LINKING</text>
                        <text x="375" y="590" text-anchor="middle" font-size="10">Previous block</text>
                        <text x="375" y="605" text-anchor="middle" font-size="10">hash in header</text>
                    </g>

                    <!-- Merkle Trees -->
                    <g>
                        <rect x="500" y="550" width="150" height="60" fill="#FFF3E0" stroke="#FF9800" stroke-width="2"/>
                        <text x="575" y="575" text-anchor="middle" font-size="12" font-weight="bold">MERKLE TREES</text>
                        <text x="575" y="590" text-anchor="middle" font-size="10">Transaction</text>
                        <text x="575" y="605" text-anchor="middle" font-size="10">verification</text>
                    </g>

                    <!-- Mining -->
                    <g>
                        <rect x="700" y="550" width="150" height="60" fill="#E1BEE7" stroke="#9C27B0" stroke-width="2"/>
                        <text x="775" y="575" text-anchor="middle" font-size="12" font-weight="bold">MINING</text>
                        <text x="775" y="590" text-anchor="middle" font-size="10">Proof of Work</text>
                        <text x="775" y="605" text-anchor="middle" font-size="10">difficulty target</text>
                    </g>

                    <!-- Digital Signatures -->
                    <g>
                        <rect x="900" y="550" width="150" height="60" fill="#FFCDD2" stroke="#F44336" stroke-width="2"/>
                        <text x="975" y="575" text-anchor="middle" font-size="12" font-weight="bold">SIGNATURES</text>
                        <text x="975" y="590" text-anchor="middle" font-size="10">Hash message</text>
                        <text x="975" y="605" text-anchor="middle" font-size="10">before signing</text>
                    </g>

                    <!-- Security Note -->
                    <text x="600" y="650" text-anchor="middle" font-size="14" fill="#F44336" font-weight="bold">
                        Security Foundation: Hash functions provide integrity, authenticity, and immutability
                    </text>

                    <!-- Arrow marker definition -->
                    <defs>
                        <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
                        </marker>
                    </defs>
                </svg>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">⚙️</span>How Hash Functions Work in Blockchain</h2>

            <div class="example-box">
                <h3>Blockchain Hash Function Applications</h3>
                <div class="code-snippet">
1. BLOCK IDENTIFICATION:
   Block Header + Transactions → SHA-256 → Block Hash
   Each block has unique hash identifier

2. CHAIN LINKING:
   Block N contains hash of Block N-1
   Creates immutable chain structure

3. MERKLE TREE CONSTRUCTION:
   Hash(Transaction A) + Hash(Transaction B) → Hash(AB)
   Efficient transaction verification

4. MINING PROCESS:
   Block Header + Nonce → SHA-256 → Target Hash
   Miners find nonce that produces hash below target

5. DIGITAL SIGNATURES:
   Hash(Message) → Sign with private key
   Prevents signature of large data

6. ADDRESS GENERATION:
   Public Key → Hash → Wallet Address
   Creates shorter, user-friendly addresses
                </div>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">✅</span>Security Benefits</h2>

            <div class="advantages-box">
                <h3>Key Security Advantages</h3>
                <ul>
                    <li><strong>Immutability:</strong> Changing data requires changing all subsequent blocks</li>
                    <li><strong>Integrity Verification:</strong> Easy to detect any data tampering</li>
                    <li><strong>Efficient Storage:</strong> Large data represented by small hash</li>
                    <li><strong>Privacy:</strong> Original data cannot be derived from hash</li>
                    <li><strong>Authentication:</strong> Proves data hasn't been modified</li>
                    <li><strong>Non-repudiation:</strong> Cannot deny creating specific hash</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">❌</span>Limitations and Challenges</h2>

            <div class="disadvantages-box">
                <h3>Technical Limitations</h3>
                <ul>
                    <li><strong>Computational Cost:</strong> Complex calculations require processing power</li>
                    <li><strong>Hash Collisions:</strong> Theoretical possibility of same hash for different inputs</li>
                    <li><strong>Quantum Vulnerability:</strong> Future quantum computers may break current algorithms</li>
                    <li><strong>Algorithm Aging:</strong> Hash functions may become obsolete over time</li>
                    <li><strong>Storage Requirements:</strong> Every hash must be stored and transmitted</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">🚀</span>Blockchain Applications</h2>

            <div class="example-box">
                <h3>Real-World Implementations</h3>
                <ul>
                    <li><strong>Bitcoin (SHA-256):</strong> Mining and block identification</li>
                    <li><strong>Ethereum (Keccak-256):</strong> Account addresses and state verification</li>
                    <li><strong>Merkle Trees:</strong> Efficient transaction verification</li>
                    <li><strong>Proof of Work:</strong> Mining difficulty and consensus</li>
                    <li><strong>Digital Wallets:</strong> Address generation and key derivation</li>
                    <li><strong>Smart Contracts:</strong> Code verification and execution</li>
                </ul>
            </div>
        </div>

    </div>
</body>
</html>
