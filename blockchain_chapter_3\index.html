<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blockchain Chapter 3: Consensus Mechanisms & Network Security</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #4facfe 100%);
            min-height: 100vh;
            line-height: 1.6;
            overflow-x: hidden;
        }

        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #4facfe 100%);
            animation: gradientShift 25s ease infinite;
        }

        @keyframes gradientShift {
            0% { background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #4facfe 100%); }
            20% { background: linear-gradient(135deg, #f093fb 0%, #4facfe 30%, #667eea 60%, #764ba2 100%); }
            40% { background: linear-gradient(135deg, #4facfe 0%, #667eea 30%, #764ba2 60%, #f093fb 100%); }
            60% { background: linear-gradient(135deg, #764ba2 0%, #f093fb 30%, #4facfe 60%, #667eea 100%); }
            80% { background: linear-gradient(135deg, #667eea 0%, #4facfe 30%, #f093fb 60%, #764ba2 100%); }
            100% { background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #4facfe 100%); }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
            padding: 50px 30px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 30px;
            box-shadow: 0 25px 70px rgba(0,0,0,0.15);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .main-title {
            font-size: 4em;
            font-weight: 900;
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb, #4facfe);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.1);
            position: relative;
            z-index: 1;
        }

        .subtitle {
            font-size: 1.6em;
            color: #555;
            margin-bottom: 30px;
            font-weight: 400;
            position: relative;
            z-index: 1;
        }

        .chapter-info {
            display: flex;
            justify-content: center;
            gap: 40px;
            flex-wrap: wrap;
            margin-top: 30px;
            position: relative;
            z-index: 1;
        }

        .info-badge {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px 30px;
            border-radius: 30px;
            font-weight: 700;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
            transform: translateY(0);
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .info-badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s ease;
        }

        .info-badge:hover::before {
            left: 100%;
        }

        .info-badge:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.5);
        }

        .intro-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 50px;
            margin-bottom: 60px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .intro-section h2 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 25px;
            font-weight: 800;
        }

        .intro-section p {
            color: #666;
            font-size: 1.2em;
            margin-bottom: 20px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .feature-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
            padding: 30px;
            border-radius: 20px;
            border-left: 6px solid #667eea;
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            transition: left 0.6s ease;
        }

        .feature-card:hover::before {
            left: 100%;
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 60px rgba(0,0,0,0.2);
        }

        .questions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
            gap: 35px;
            margin: 50px 0;
        }

        .question-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 35px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            transition: all 0.5s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .question-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.5), transparent);
            transition: left 0.7s ease;
        }

        .question-card:hover::before {
            left: 100%;
        }

        .question-card:hover {
            transform: translateY(-12px) scale(1.02);
            box-shadow: 0 35px 80px rgba(0,0,0,0.25);
        }

        .question-number {
            font-size: 3.5em;
            font-weight: 900;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .question-title {
            font-size: 1.5em;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 18px;
            line-height: 1.3;
        }

        .question-description {
            color: #666;
            font-size: 1.1em;
            margin-bottom: 25px;
            line-height: 1.7;
        }

        .question-topics {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-bottom: 20px;
        }

        .topic-tag {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 0.9em;
            font-weight: 600;
            box-shadow: 0 5px 20px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
        }

        .topic-tag:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .difficulty-indicator {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-top: 20px;
        }

        .difficulty-label {
            font-size: 1em;
            color: #666;
            font-weight: 600;
        }

        .difficulty-stars {
            display: flex;
            gap: 4px;
        }

        .star {
            width: 14px;
            height: 14px;
            background: #ddd;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .star.filled {
            background: linear-gradient(135deg, #667eea, #764ba2);
            box-shadow: 0 3px 10px rgba(102, 126, 234, 0.4);
        }

        .emoji {
            font-size: 2em;
            margin-right: 15px;
            filter: drop-shadow(0 3px 6px rgba(0,0,0,0.1));
        }

        .footer {
            text-align: center;
            margin-top: 80px;
            padding: 50px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .footer h3 {
            color: #2c3e50;
            margin-bottom: 25px;
            font-size: 2em;
        }

        .footer p {
            color: #666;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        a {
            text-decoration: none;
            color: inherit;
        }

        @media (max-width: 768px) {
            .main-title {
                font-size: 2.8em;
            }

            .questions-grid {
                grid-template-columns: 1fr;
                gap: 25px;
            }

            .chapter-info {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="animated-bg"></div>

    <div class="container">
        <div class="header">
            <h1 class="main-title">🔗 Blockchain Chapter 3</h1>
            <p class="subtitle">Consensus Mechanisms & Network Security</p>

            <div class="chapter-info">
                <div class="info-badge">🎯 10 Advanced Topics</div>
                <div class="info-badge">🔒 Security Focus</div>
                <div class="info-badge">⚡ Interactive Diagrams</div>
                <div class="info-badge">🌍 Real-World Examples</div>
            </div>
        </div>

        <div class="intro-section">
            <h2>🛡️ Consensus & Security Deep Dive</h2>
            <p>Welcome to Chapter 3, where we explore the heart of blockchain security - consensus mechanisms. This chapter provides comprehensive coverage of how blockchain networks achieve agreement, maintain security, and handle various attack vectors.</p>

            <div class="features-grid">
                <div class="feature-card">
                    <h3>🎨 Enhanced Visualizations</h3>
                    <p>Interactive block diagrams with detailed explanations of consensus mechanisms and security protocols</p>
                </div>
                <div class="feature-card">
                    <h3>🔬 Technical Analysis</h3>
                    <p>Deep dive into cryptographic proofs, economic incentives, and mathematical foundations</p>
                </div>
                <div class="feature-card">
                    <h3>🌍 Real-World Examples</h3>
                    <p>Case studies from Bitcoin, Ethereum, and other major blockchain networks</p>
                </div>
                <div class="feature-card">
                    <h3>⚖️ Security Assessment</h3>
                    <p>Comprehensive analysis of attack vectors, vulnerabilities, and mitigation strategies</p>
                </div>
            </div>
        </div>

        <div class="questions-grid">
            <a href="question_1_nakamoto_consensus.html" class="question-card">
                <div class="question-number">01</div>
                <h3 class="question-title"><span class="emoji">🏛️</span>Nakamoto Consensus</h3>
                <p class="question-description">Understanding the foundational consensus mechanism that powers Bitcoin and how it secures blockchain networks through cryptographic proof and economic incentives.</p>
                <div class="question-topics">
                    <span class="topic-tag">Consensus Theory</span>
                    <span class="topic-tag">Bitcoin Protocol</span>
                    <span class="topic-tag">Network Security</span>
                </div>
                <div class="difficulty-indicator">
                    <span class="difficulty-label">Difficulty:</span>
                    <div class="difficulty-stars">
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star"></div>
                        <div class="star"></div>
                    </div>
                </div>
            </a>

            <a href="question_2_proof_of_work.html" class="question-card">
                <div class="question-number">02</div>
                <h3 class="question-title"><span class="emoji">⛏️</span>Proof of Work (PoW)</h3>
                <p class="question-description">Deep dive into the computational consensus mechanism with practical examples, mining process, and real-world implementations in major cryptocurrencies.</p>
                <div class="question-topics">
                    <span class="topic-tag">Mining</span>
                    <span class="topic-tag">Cryptographic Puzzles</span>
                    <span class="topic-tag">Hash Functions</span>
                </div>
                <div class="difficulty-indicator">
                    <span class="difficulty-label">Difficulty:</span>
                    <div class="difficulty-stars">
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star"></div>
                        <div class="star"></div>
                    </div>
                </div>
            </a>

            <a href="question_3_proof_of_stake.html" class="question-card">
                <div class="question-number">03</div>
                <h3 class="question-title"><span class="emoji">🏦</span>Proof of Stake (PoS)</h3>
                <p class="question-description">Comprehensive analysis of the energy-efficient consensus mechanism, including advantages, disadvantages, and real implementations like Ethereum 2.0.</p>
                <div class="question-topics">
                    <span class="topic-tag">Staking</span>
                    <span class="topic-tag">Validators</span>
                    <span class="topic-tag">Energy Efficiency</span>
                </div>
                <div class="difficulty-indicator">
                    <span class="difficulty-label">Difficulty:</span>
                    <div class="difficulty-stars">
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star"></div>
                    </div>
                </div>
            </a>

            <a href="question_4_proof_of_burn.html" class="question-card">
                <div class="question-number">04</div>
                <h3 class="question-title"><span class="emoji">🔥</span>Proof of Burn (PoB)</h3>
                <p class="question-description">Exploring the unique consensus mechanism where participants destroy coins to gain mining rights, including working principles and real-world applications.</p>
                <div class="question-topics">
                    <span class="topic-tag">Coin Burning</span>
                    <span class="topic-tag">Virtual Mining</span>
                    <span class="topic-tag">Economic Theory</span>
                </div>
                <div class="difficulty-indicator">
                    <span class="difficulty-label">Difficulty:</span>
                    <div class="difficulty-stars">
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star"></div>
                    </div>
                </div>
            </a>

            <a href="question_5_difficulty_adjustment.html" class="question-card">
                <div class="question-number">05</div>
                <h3 class="question-title"><span class="emoji">⚖️</span>Difficulty Adjustment</h3>
                <p class="question-description">Understanding how blockchain networks automatically adjust mining difficulty to maintain consistent block times and network stability.</p>
                <div class="question-topics">
                    <span class="topic-tag">Mining Difficulty</span>
                    <span class="topic-tag">Network Stability</span>
                    <span class="topic-tag">Algorithms</span>
                </div>
                <div class="difficulty-indicator">
                    <span class="difficulty-label">Difficulty:</span>
                    <div class="difficulty-stars">
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star"></div>
                        <div class="star"></div>
                    </div>
                </div>
            </a>

            <a href="question_6_sybil_attack.html" class="question-card">
                <div class="question-number">06</div>
                <h3 class="question-title"><span class="emoji">👥</span>Sybil Attack</h3>
                <p class="question-description">Analyzing one of the most critical security threats to blockchain networks and the various mitigation strategies employed by different consensus mechanisms.</p>
                <div class="question-topics">
                    <span class="topic-tag">Network Security</span>
                    <span class="topic-tag">Attack Vectors</span>
                    <span class="topic-tag">Identity Verification</span>
                </div>
                <div class="difficulty-indicator">
                    <span class="difficulty-label">Difficulty:</span>
                    <div class="difficulty-stars">
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star"></div>
                    </div>
                </div>
            </a>

            <a href="question_7_energy_consumption.html" class="question-card">
                <div class="question-number">07</div>
                <h3 class="question-title"><span class="emoji">⚡</span>Energy Consumption</h3>
                <p class="question-description">Examining the environmental impact of Proof of Work blockchains, energy consumption patterns, and sustainable alternatives.</p>
                <div class="question-topics">
                    <span class="topic-tag">Environmental Impact</span>
                    <span class="topic-tag">Sustainability</span>
                    <span class="topic-tag">Green Mining</span>
                </div>
                <div class="difficulty-indicator">
                    <span class="difficulty-label">Difficulty:</span>
                    <div class="difficulty-stars">
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star"></div>
                        <div class="star"></div>
                    </div>
                </div>
            </a>

            <a href="question_8_alternative_consensus.html" class="question-card">
                <div class="question-number">08</div>
                <h3 class="question-title"><span class="emoji">🔄</span>Alternative Consensus</h3>
                <p class="question-description">Exploring innovative consensus mechanisms beyond PoW and PoS, including DPoS, PoA, PoH, and emerging hybrid approaches.</p>
                <div class="question-topics">
                    <span class="topic-tag">Innovation</span>
                    <span class="topic-tag">Hybrid Mechanisms</span>
                    <span class="topic-tag">Scalability</span>
                </div>
                <div class="difficulty-indicator">
                    <span class="difficulty-label">Difficulty:</span>
                    <div class="difficulty-stars">
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                    </div>
                </div>
            </a>

            <a href="question_9_economic_incentives.html" class="question-card">
                <div class="question-number">09</div>
                <h3 class="question-title"><span class="emoji">💰</span>Economic Incentives</h3>
                <p class="question-description">Understanding the economic models that drive blockchain participation, including rewards, fees, and game theory behind mining incentives.</p>
                <div class="question-topics">
                    <span class="topic-tag">Game Theory</span>
                    <span class="topic-tag">Tokenomics</span>
                    <span class="topic-tag">Incentive Design</span>
                </div>
                <div class="difficulty-indicator">
                    <span class="difficulty-label">Difficulty:</span>
                    <div class="difficulty-stars">
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star"></div>
                    </div>
                </div>
            </a>

            <a href="question_10_fair_participation.html" class="question-card">
                <div class="question-number">10</div>
                <h3 class="question-title"><span class="emoji">⚖️</span>Fair Participation</h3>
                <p class="question-description">Analyzing how blockchain networks ensure equitable participation in consensus mechanisms and prevent centralization of power.</p>
                <div class="question-topics">
                    <span class="topic-tag">Decentralization</span>
                    <span class="topic-tag">Governance</span>
                    <span class="topic-tag">Network Fairness</span>
                </div>
                <div class="difficulty-indicator">
                    <span class="difficulty-label">Difficulty:</span>
                    <div class="difficulty-stars">
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star"></div>
                    </div>
                </div>
            </a>
        </div>

        <div class="footer">
            <h3>🎓 Ready to Master Consensus Mechanisms?</h3>
            <p>Each question includes detailed block diagrams, real-world examples, and comprehensive analysis.</p>
            <p>Click on any question above to begin your deep dive into blockchain consensus and security.</p>
            <p><strong>Prerequisites:</strong> Completion of Chapters 1 & 2 recommended</p>
        </div>
    </div>
</body>
</html>
