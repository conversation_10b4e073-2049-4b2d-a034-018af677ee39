<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Question 2: Proof of Work (PoW)</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #4facfe 100%);
            min-height: 100vh; line-height: 1.6; overflow-x: hidden;
        }
        .animated-bg {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: -1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #4facfe 100%);
            animation: gradientShift 25s ease infinite;
        }
        @keyframes gradientShift {
            0% { background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #4facfe 100%); }
            25% { background: linear-gradient(135deg, #f093fb 0%, #4facfe 30%, #667eea 60%, #764ba2 100%); }
            50% { background: linear-gradient(135deg, #4facfe 0%, #667eea 30%, #764ba2 60%, #f093fb 100%); }
            75% { background: linear-gradient(135deg, #764ba2 0%, #f093fb 30%, #4facfe 60%, #667eea 100%); }
            100% { background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #4facfe 100%); }
        }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header {
            text-align: center; margin-bottom: 40px; padding: 40px 20px;
            background: rgba(255, 255, 255, 0.95); border-radius: 25px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1); backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .question-number {
            font-size: 4em; font-weight: 900; margin-bottom: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb, #4facfe);
            -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;
        }
        .main-title { font-size: 2.8em; font-weight: 700; color: #2c3e50; margin-bottom: 15px; }
        .subtitle { font-size: 1.3em; color: #666; margin-bottom: 25px; }
        .section {
            margin: 40px 0; padding: 35px; background: rgba(255, 255, 255, 0.95);
            border-radius: 20px; box-shadow: 0 15px 45px rgba(0,0,0,0.1);
            backdrop-filter: blur(15px); border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        .section:hover { transform: translateY(-5px); box-shadow: 0 25px 60px rgba(0,0,0,0.15); }
        .section h2 {
            font-size: 2.2em; color: #2c3e50; margin-bottom: 25px; font-weight: 700;
            display: flex; align-items: center; gap: 15px;
        }
        .emoji { font-size: 1.2em; filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1)); }
        .concept-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 15px; padding: 30px; margin: 25px 0;
            border-left: 6px solid #2196f3; box-shadow: 0 8px 25px rgba(33, 150, 243, 0.1);
        }
        .highlight {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            padding: 4px 10px; border-radius: 6px; font-weight: 600;
            color: #856404; display: inline-block;
        }
        .diagram-container {
            background: rgba(248, 249, 250, 0.95); border: 2px solid rgba(222, 226, 230, 0.5);
            border-radius: 15px; padding: 25px; margin: 25px 0; overflow-x: auto; backdrop-filter: blur(10px);
        }
        .mining-steps { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 30px 0; }
        .step-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
            border-radius: 15px; padding: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease; border-top: 4px solid #FF9800;
        }
        .step-card:hover { transform: translateY(-8px); box-shadow: 0 20px 50px rgba(0,0,0,0.15); }
        .step-number {
            font-size: 2em; font-weight: 900; color: #FF9800; margin-bottom: 10px;
        }
        .example-card {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border-radius: 15px; padding: 25px; margin: 20px 0;
            border-left: 6px solid #4caf50; box-shadow: 0 8px 25px rgba(76, 175, 80, 0.1);
        }
        .code-block {
            background: #2d3748; color: #e2e8f0; padding: 20px; border-radius: 10px;
            font-family: 'Courier New', monospace; margin: 15px 0; overflow-x: auto;
        }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 25px 0; }
        .stat-card {
            background: linear-gradient(135deg, #FF9800, #F57C00); color: white; padding: 20px;
            border-radius: 12px; text-align: center; box-shadow: 0 8px 25px rgba(255, 152, 0, 0.3);
            transition: all 0.3s ease;
        }
        .stat-card:hover { transform: translateY(-5px); box-shadow: 0 12px 35px rgba(255, 152, 0, 0.4); }
        .stat-number { font-size: 2.5em; font-weight: 900; margin-bottom: 10px; }
        .stat-label { font-size: 1.1em; opacity: 0.9; }
        @media (max-width: 768px) {
            .main-title { font-size: 2.2em; } .question-number { font-size: 3em; }
            .mining-steps { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="animated-bg"></div>
    <div class="container">
        <div class="header">
            <div class="question-number">02</div>
            <h1 class="main-title">⛏️ Proof of Work (PoW)</h1>
            <p class="subtitle">Computational Consensus Through Cryptographic Puzzles</p>
        </div>

        <div class="section">
            <h2><span class="emoji">🎯</span>Understanding Proof of Work</h2>
            <div class="concept-box">
                <h3>The Computational Consensus Mechanism</h3>
                <p><span class="highlight">Proof of Work (PoW)</span> is a consensus mechanism where network participants (miners) compete to solve computationally intensive cryptographic puzzles to validate transactions and create new blocks. The first miner to solve the puzzle broadcasts the solution, and other nodes verify it before accepting the block.</p>

                <p><strong>Core Concept:</strong> PoW transforms the problem of achieving consensus into a race to find a specific mathematical solution. This creates an objective, verifiable way to determine which participant has the right to add the next block to the blockchain.</p>

                <p><strong>Key Properties:</strong></p>
                <ul>
                    <li><strong>Asymmetric Difficulty:</strong> Hard to solve, easy to verify</li>
                    <li><strong>Deterministic:</strong> Same input always produces same output</li>
                    <li><strong>Avalanche Effect:</strong> Small input changes drastically alter output</li>
                    <li><strong>Unpredictable:</strong> No way to predict solution without computation</li>
                </ul>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">10</div>
                    <div class="stat-label">Minutes Average Block Time</div>
                </div>

                <div class="stat-card">
                    <div class="stat-number">400+</div>
                    <div class="stat-label">Exahashes/sec Network Rate</div>
                </div>

                <div class="stat-card">
                    <div class="stat-number">21,000</div>
                    <div class="stat-label">Gas for ETH Transfer</div>
                </div>

                <div class="stat-card">
                    <div class="stat-number">6.25</div>
                    <div class="stat-label">BTC Block Reward</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">🔧</span>How Proof of Work Functions</h2>

            <div class="diagram-container">
                <svg width="100%" height="700" viewBox="0 0 1200 700">
                    <!-- Title -->
                    <text x="600" y="30" text-anchor="middle" font-size="20" font-weight="bold">PROOF OF WORK MINING PROCESS</text>

                    <!-- Step 1: Transaction Collection -->
                    <g id="step1" transform="translate(50, 70)">
                        <rect x="0" y="0" width="200" height="100" fill="#E3F2FD" stroke="#2196F3" stroke-width="2" rx="10"/>
                        <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="bold">1. COLLECT TRANSACTIONS</text>
                        <text x="100" y="45" text-anchor="middle" font-size="11">Miner gathers pending</text>
                        <text x="100" y="60" text-anchor="middle" font-size="11">transactions from mempool</text>
                        <text x="100" y="80" text-anchor="middle" font-size="10">📝 Tx1, Tx2, Tx3...</text>
                    </g>

                    <!-- Step 2: Block Assembly -->
                    <g id="step2" transform="translate(300, 70)">
                        <rect x="0" y="0" width="200" height="100" fill="#FFF3E0" stroke="#FF9800" stroke-width="2" rx="10"/>
                        <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="bold">2. ASSEMBLE BLOCK</text>
                        <text x="100" y="45" text-anchor="middle" font-size="11">Create block header with</text>
                        <text x="100" y="60" text-anchor="middle" font-size="11">previous hash, merkle root</text>
                        <text x="100" y="80" text-anchor="middle" font-size="10">🔗 Header + Nonce</text>
                    </g>

                    <!-- Step 3: Hash Computation -->
                    <g id="step3" transform="translate(550, 70)">
                        <rect x="0" y="0" width="200" height="100" fill="#FFEBEE" stroke="#F44336" stroke-width="2" rx="10"/>
                        <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="bold">3. COMPUTE HASH</text>
                        <text x="100" y="45" text-anchor="middle" font-size="11">Apply SHA-256 twice</text>
                        <text x="100" y="60" text-anchor="middle" font-size="11">to block header</text>
                        <text x="100" y="80" text-anchor="middle" font-size="10">🧮 SHA-256(SHA-256(header))</text>
                    </g>

                    <!-- Step 4: Target Check -->
                    <g id="step4" transform="translate(800, 70)">
                        <rect x="0" y="0" width="200" height="100" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2" rx="10"/>
                        <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="bold">4. CHECK TARGET</text>
                        <text x="100" y="45" text-anchor="middle" font-size="11">Hash &lt; Target?</text>
                        <text x="100" y="60" text-anchor="middle" font-size="11">Leading zeros check</text>
                        <text x="100" y="80" text-anchor="middle" font-size="10">🎯 000000abc123...</text>
                    </g>

                    <!-- Arrows between steps -->
                    <path d="M 250 120 L 300 120" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                    <path d="M 500 120 L 550 120" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                    <path d="M 750 120 L 800 120" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>

                    <!-- Decision Diamond -->
                    <g id="decision" transform="translate(900, 200)">
                        <polygon points="100,50 150,100 100,150 50,100" fill="#FFF3E0" stroke="#FF9800" stroke-width="3"/>
                        <text x="100" y="95" text-anchor="middle" font-size="12" font-weight="bold">Valid?</text>
                        <text x="100" y="110" text-anchor="middle" font-size="10">Hash &lt; Target</text>
                    </g>

                    <!-- Success Path -->
                    <g id="success" transform="translate(750, 400)">
                        <rect x="0" y="0" width="200" height="100" fill="#C8E6C9" stroke="#4CAF50" stroke-width="3" rx="10"/>
                        <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="bold">✅ BLOCK FOUND!</text>
                        <text x="100" y="45" text-anchor="middle" font-size="11">Broadcast to network</text>
                        <text x="100" y="60" text-anchor="middle" font-size="11">Collect rewards</text>
                        <text x="100" y="80" text-anchor="middle" font-size="10">💰 6.25 BTC + fees</text>
                    </g>

                    <!-- Failure Path -->
                    <g id="failure" transform="translate(400, 300)">
                        <rect x="0" y="0" width="200" height="80" fill="#FFCDD2" stroke="#F44336" stroke-width="2" rx="10"/>
                        <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="bold">❌ TRY AGAIN</text>
                        <text x="100" y="45" text-anchor="middle" font-size="11">Increment nonce</text>
                        <text x="100" y="60" text-anchor="middle" font-size="11">Repeat process</text>
                    </g>

                    <!-- Decision arrows -->
                    <path d="M 900 250 L 850 400" stroke="#4CAF50" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                    <text x="820" y="320" font-size="12" font-weight="bold" fill="#4CAF50">YES</text>

                    <path d="M 850 250 L 500 340" stroke="#F44336" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                    <text x="650" y="280" font-size="12" font-weight="bold" fill="#F44336">NO</text>

                    <!-- Loop back arrow -->
                    <path d="M 400 340 L 200 340 L 200 200 L 650 200" stroke="#F44336" stroke-width="2" fill="none" marker-end="url(#arrow)" stroke-dasharray="5,5"/>

                    <!-- Hash Example -->
                    <g id="hash-example" transform="translate(50, 550)">
                        <text x="0" y="20" font-size="14" font-weight="bold">HASH EXAMPLE:</text>
                        <text x="0" y="45" font-size="11">Block Header: </text>
                        <text x="120" y="45" font-size="10" font-family="monospace">01000000...nonce: 2083236893</text>
                        <text x="0" y="65" font-size="11">SHA-256 Hash: </text>
                        <text x="120" y="65" font-size="10" font-family="monospace" fill="#4CAF50">00000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f</text>
                        <text x="0" y="85" font-size="11">Target: </text>
                        <text x="120" y="85" font-size="10" font-family="monospace">00000000000404CB000000000000000000000000000000000000000000000000</text>
                        <text x="0" y="105" font-size="11" fill="#4CAF50" font-weight="bold">✓ Hash &lt; Target = Valid Block!</text>
                    </g>

                    <!-- Difficulty Explanation -->
                    <g id="difficulty" transform="translate(600, 550)">
                        <text x="0" y="20" font-size="14" font-weight="bold">DIFFICULTY ADJUSTMENT:</text>
                        <text x="0" y="45" font-size="11">• Target adjusts every 2016 blocks (~2 weeks)</text>
                        <text x="0" y="65" font-size="11">• Maintains ~10 minute block time</text>
                        <text x="0" y="85" font-size="11">• More miners = higher difficulty</text>
                        <text x="0" y="105" font-size="11">• Fewer miners = lower difficulty</text>
                    </g>

                    <!-- Arrow marker definition -->
                    <defs>
                        <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
                        </marker>
                    </defs>
                </svg>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">📝</span>Step-by-Step Mining Process</h2>
            <div class="mining-steps">
                <div class="step-card">
                    <div class="step-number">1</div>
                    <h3>Transaction Collection</h3>
                    <p>Miners select pending transactions from the mempool, prioritizing those with higher fees. They verify each transaction's validity before inclusion.</p>
                </div>

                <div class="step-card">
                    <div class="step-number">2</div>
                    <h3>Merkle Tree Construction</h3>
                    <p>Transactions are organized into a Merkle tree, creating a single root hash that represents all transactions in the block.</p>
                </div>

                <div class="step-card">
                    <div class="step-number">3</div>
                    <h3>Block Header Assembly</h3>
                    <p>Create block header containing: previous block hash, Merkle root, timestamp, difficulty target, and nonce (initially 0).</p>
                </div>

                <div class="step-card">
                    <div class="step-number">4</div>
                    <h3>Hash Computation</h3>
                    <p>Apply SHA-256 hash function twice to the block header. This produces a 256-bit hash that must meet the difficulty target.</p>
                </div>

                <div class="step-card">
                    <div class="step-number">5</div>
                    <h3>Target Verification</h3>
                    <p>Check if the computed hash is less than the current difficulty target (has required number of leading zeros).</p>
                </div>

                <div class="step-card">
                    <div class="step-number">6</div>
                    <h3>Nonce Increment</h3>
                    <p>If hash doesn't meet target, increment nonce by 1 and repeat. Miners try billions of nonce values per second.</p>
                </div>

        <div class="section">
            <h2><span class="emoji">💻</span>Practical Example: Bitcoin Mining</h2>

            <div class="example-card">
                <h3>🟠 Bitcoin Block #700,000 - Real Mining Example</h3>
                <p><strong>Mined on September 11, 2021, by F2Pool</strong></p>

                <div class="code-block">
Block Height: 700,000
Hash: 000000000000000000024bead8df69990852c202db0e0097c1a12ea637d7e96d
Previous Block: 0000000000000000000c4a5cdd4c8c2c0c8e4b4e8c4c8c2c0c8e4b4e8c4c8c2c
Merkle Root: 8b30c5ba100f6f2e5ad1e2a742e5020491240f8eb514fe97c713c31718ad7ecd
Timestamp: 1631375134 (Sep 11, 2021 14:32:14 GMT)
Difficulty: 17,615,033,039,278.88
Nonce: 1,361,599,093
Transactions: 2,875
Block Size: 1.37 MB
Block Reward: 6.25 BTC
Total Fees: 0.1847 BTC
                </div>

                <p><strong>Mining Statistics:</strong></p>
                <ul>
                    <li><strong>Hash Rate Required:</strong> ~150 EH/s network hash rate at the time</li>
                    <li><strong>Attempts:</strong> Approximately 1.36 billion nonce attempts before success</li>
                    <li><strong>Energy:</strong> Estimated 700-900 kWh consumed by winning miner</li>
                    <li><strong>Competition:</strong> Thousands of miners worldwide competing simultaneously</li>
                    <li><strong>Verification:</strong> Block verified by 15,000+ nodes within minutes</li>
                </ul>
            </div>

            <div class="example-card">
                <h3>⚡ Litecoin - Scrypt Algorithm Example</h3>
                <p><strong>Alternative PoW implementation with different hash function</strong></p>

                <div class="code-block">
Algorithm: Scrypt (memory-hard function)
Block Time: 2.5 minutes (4x faster than Bitcoin)
Difficulty Adjustment: Every 2016 blocks
Current Difficulty: ~20,000,000
Hash Rate: ~500 TH/s
Block Reward: 12.5 LTC (halves every 840,000 blocks)
                </div>

                <p><strong>Key Differences from Bitcoin:</strong></p>
                <ul>
                    <li><strong>Memory Requirements:</strong> Scrypt requires more RAM, making ASIC development harder</li>
                    <li><strong>Faster Confirmations:</strong> 2.5-minute blocks provide quicker transaction finality</li>
                    <li><strong>Different Mining Hardware:</strong> Initially GPU-friendly, now has specialized ASICs</li>
                    <li><strong>Lower Energy per Transaction:</strong> Faster blocks reduce energy per transaction</li>
                </ul>
            </div>

            <div class="example-card">
                <h3>💎 Ethereum Classic - Ethash Algorithm</h3>
                <p><strong>ASIC-resistant PoW designed for GPU mining</strong></p>

                <div class="code-block">
Algorithm: Ethash (DAG-based)
Block Time: ~13 seconds
DAG Size: ~4.3 GB (grows over time)
Memory Requirement: 4+ GB GPU memory
Hash Rate: ~25 TH/s
Block Reward: 3.2 ETC + fees
                </div>

                <p><strong>Ethash Innovations:</strong></p>
                <ul>
                    <li><strong>DAG (Directed Acyclic Graph):</strong> Large dataset that changes every 30,000 blocks</li>
                    <li><strong>Memory-Hard:</strong> Requires substantial memory bandwidth</li>
                    <li><strong>ASIC Resistance:</strong> Designed to favor GPU mining over specialized hardware</li>
                    <li><strong>Progressive Difficulty:</strong> DAG size increases over time</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">🔬</span>Technical Deep Dive: Hash Functions</h2>

            <div class="concept-box">
                <h3>SHA-256: The Heart of Bitcoin Mining</h3>
                <p>Bitcoin uses the SHA-256 (Secure Hash Algorithm 256-bit) cryptographic hash function, applied twice for additional security. Understanding how this function works is crucial to understanding PoW.</p>

                <div class="code-block">
// Simplified SHA-256 Process
Input: Block Header (80 bytes)
Step 1: SHA-256(block_header) → intermediate_hash
Step 2: SHA-256(intermediate_hash) → final_hash
Output: 256-bit hash (64 hexadecimal characters)

Example:
Input: "Hello, Bitcoin!"
SHA-256: 1c7c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c
Double SHA-256: 6b88c087247aa2f07ee1c5956b8e1a9f4c7f8d9e0a1b2c3d4e5f6789abcdef01
                </div>

                <p><strong>SHA-256 Properties:</strong></p>
                <ul>
                    <li><strong>Deterministic:</strong> Same input always produces same output</li>
                    <li><strong>Fixed Output Size:</strong> Always 256 bits regardless of input size</li>
                    <li><strong>Avalanche Effect:</strong> Tiny input change completely changes output</li>
                    <li><strong>One-Way Function:</strong> Computationally infeasible to reverse</li>
                    <li><strong>Collision Resistant:</strong> Extremely difficult to find two inputs with same output</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">🌍</span>Real-World Mining Operations</h2>

            <div class="example-card">
                <h3>🏭 Industrial Mining Farms</h3>
                <p><strong>Large-scale operations that dominate modern Bitcoin mining</strong></p>
                <ul>
                    <li><strong>Bitmain Antminer S19 Pro:</strong> 110 TH/s, 3250W power consumption</li>
                    <li><strong>Facility Scale:</strong> 10,000+ ASIC miners in single warehouse</li>
                    <li><strong>Power Requirements:</strong> 50-100 MW electrical capacity</li>
                    <li><strong>Cooling Systems:</strong> Industrial air conditioning and immersion cooling</li>
                    <li><strong>Geographic Distribution:</strong> Located near cheap electricity sources</li>
                    <li><strong>Pool Mining:</strong> Most farms join mining pools to reduce variance</li>
                </ul>
            </div>

            <div class="example-card">
                <h3>🌊 Renewable Energy Mining</h3>
                <p><strong>Sustainable mining operations using clean energy sources</strong></p>
                <ul>
                    <li><strong>Hydroelectric:</strong> Mining farms near dams in Washington, Quebec, Iceland</li>
                    <li><strong>Solar Power:</strong> Desert installations in Nevada, Texas, Australia</li>
                    <li><strong>Wind Energy:</strong> Mining operations in windy regions of Texas, Wyoming</li>
                    <li><strong>Geothermal:</strong> Iceland and El Salvador utilizing volcanic energy</li>
                    <li><strong>Stranded Gas:</strong> Converting waste gas from oil fields into electricity</li>
                    <li><strong>Grid Balancing:</strong> Miners as flexible load for renewable energy grids</li>
                </ul>
            </div>

            <div class="example-card">
                <h3>🏊 Mining Pool Examples</h3>
                <p><strong>Collaborative mining to reduce variance and ensure steady income</strong></p>
                <ul>
                    <li><strong>F2Pool:</strong> ~15% of Bitcoin hash rate, based in China</li>
                    <li><strong>AntPool:</strong> ~12% hash rate, operated by Bitmain</li>
                    <li><strong>Foundry USA:</strong> ~11% hash rate, North American focus</li>
                    <li><strong>ViaBTC:</strong> ~9% hash rate, supports multiple cryptocurrencies</li>
                    <li><strong>Slush Pool:</strong> First Bitcoin mining pool, founded 2010</li>
                    <li><strong>Payout Methods:</strong> PPS, PPLNS, SOLO mining options</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">🎯</span>Advantages and Challenges</h2>

            <div class="concept-box">
                <h3>Proof of Work: Strengths and Limitations</h3>

                <p><strong>Key Advantages:</strong></p>
                <ul>
                    <li><strong>Battle-Tested Security:</strong> 15+ years of successful operation</li>
                    <li><strong>True Decentralization:</strong> No pre-selected validators or authorities</li>
                    <li><strong>Objective Consensus:</strong> Mathematical proof of computational work</li>
                    <li><strong>Immutable History:</strong> Changing past becomes exponentially expensive</li>
                    <li><strong>Permissionless:</strong> Anyone can participate with appropriate hardware</li>
                    <li><strong>Censorship Resistant:</strong> No central authority can block transactions</li>
                </ul>

                <p><strong>Major Challenges:</strong></p>
                <ul>
                    <li><strong>Energy Consumption:</strong> Bitcoin network uses ~150 TWh annually</li>
                    <li><strong>Scalability:</strong> Limited to ~7 transactions per second</li>
                    <li><strong>Hardware Requirements:</strong> Specialized ASICs create barriers to entry</li>
                    <li><strong>Mining Centralization:</strong> Large pools control significant hash rate</li>
                    <li><strong>Environmental Concerns:</strong> Carbon footprint from electricity usage</li>
                    <li><strong>Confirmation Time:</strong> 10+ minutes for transaction finality</li>
                </ul>

                <p><strong>Key Takeaway:</strong> Proof of Work provides unmatched security and decentralization but at the cost of energy consumption and scalability. It remains the gold standard for securing high-value blockchain networks where security is paramount.</p>
            </div>
        </div>

    </div>
</body>
</html>
