<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Question 2: Blockchain Network Architecture</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            line-height: 1.6;
            overflow-x: hidden;
        }

        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            animation: gradientShift 20s ease infinite;
        }

        @keyframes gradientShift {
            0% { background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%); }
            25% { background: linear-gradient(135deg, #f093fb 0%, #667eea 50%, #764ba2 100%); }
            50% { background: linear-gradient(135deg, #764ba2 0%, #f093fb 50%, #667eea 100%); }
            75% { background: linear-gradient(135deg, #667eea 0%, #f093fb 50%, #764ba2 100%); }
            100% { background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%); }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .question-number {
            font-size: 4em;
            font-weight: 900;
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
        }

        .main-title {
            font-size: 2.8em;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .subtitle {
            font-size: 1.3em;
            color: #666;
            margin-bottom: 25px;
        }

        .section {
            margin: 40px 0;
            padding: 35px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 45px rgba(0,0,0,0.1);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .section:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 60px rgba(0,0,0,0.15);
        }

        .section h2 {
            font-size: 2.2em;
            color: #2c3e50;
            margin-bottom: 25px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .emoji {
            font-size: 1.2em;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
        }

        .concept-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 15px;
            padding: 30px;
            margin: 25px 0;
            border-left: 6px solid #2196f3;
            box-shadow: 0 8px 25px rgba(33, 150, 243, 0.1);
        }

        .node-types-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .node-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-top: 4px solid #667eea;
            position: relative;
            overflow: hidden;
        }

        .node-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s ease;
        }

        .node-card:hover::before {
            left: 100%;
        }

        .node-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 50px rgba(0,0,0,0.15);
        }

        .diagram-container {
            background: rgba(248, 249, 250, 0.95);
            border: 2px solid rgba(222, 226, 230, 0.5);
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
            overflow-x: auto;
            backdrop-filter: blur(10px);
        }

        .protocol-stack {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin: 25px 0;
        }

        .protocol-layer {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #667eea;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .protocol-layer:hover {
            transform: translateX(10px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .highlight {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            padding: 4px 10px;
            border-radius: 6px;
            font-weight: 600;
            color: #856404;
            display: inline-block;
        }

        .network-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: 900;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1.1em;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .main-title {
                font-size: 2.2em;
            }

            .question-number {
                font-size: 3em;
            }

            .node-types-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="animated-bg"></div>

    <div class="container">
        <div class="header">
            <div class="question-number">02</div>
            <h1 class="main-title">🌐 Blockchain Network Architecture</h1>
            <p class="subtitle">Understanding P2P Networks, Node Types & Communication Protocols</p>
        </div>

        <div class="section">
            <h2><span class="emoji">🏗️</span>Network Architecture Fundamentals</h2>

            <div class="concept-box">
                <h3>Peer-to-Peer Network Structure</h3>
                <p>A blockchain network is a <span class="highlight">peer-to-peer (P2P) distributed network</span> where each participant (node) maintains a copy of the entire blockchain ledger. Unlike traditional client-server architectures, there's no central authority controlling the network.</p>

                <p><strong>Core Network Principles:</strong></p>
                <ul>
                    <li><strong>Decentralization:</strong> No single point of control or failure</li>
                    <li><strong>Redundancy:</strong> Multiple copies of data across all nodes</li>
                    <li><strong>Consensus:</strong> Nodes agree on the state of the ledger</li>
                    <li><strong>Transparency:</strong> All transactions are visible to network participants</li>
                    <li><strong>Immutability:</strong> Historical data cannot be altered</li>
                </ul>
            </div>

            <div class="diagram-container">
                <svg width="100%" height="500" viewBox="0 0 1200 500">
                    <!-- Title -->
                    <text x="600" y="30" text-anchor="middle" font-size="18" font-weight="bold">BLOCKCHAIN NETWORK TOPOLOGY</text>

                    <!-- Central Network Visualization -->
                    <g>
                        <!-- Full Nodes -->
                        <circle cx="300" cy="150" r="40" fill="#4CAF50" stroke="#2E7D32" stroke-width="3"/>
                        <text x="300" y="145" text-anchor="middle" font-size="12" font-weight="bold" fill="white">FULL</text>
                        <text x="300" y="160" text-anchor="middle" font-size="12" font-weight="bold" fill="white">NODE</text>

                        <circle cx="600" cy="100" r="40" fill="#4CAF50" stroke="#2E7D32" stroke-width="3"/>
                        <text x="600" y="95" text-anchor="middle" font-size="12" font-weight="bold" fill="white">FULL</text>
                        <text x="600" y="110" text-anchor="middle" font-size="12" font-weight="bold" fill="white">NODE</text>

                        <circle cx="900" cy="150" r="40" fill="#4CAF50" stroke="#2E7D32" stroke-width="3"/>
                        <text x="900" y="145" text-anchor="middle" font-size="12" font-weight="bold" fill="white">FULL</text>
                        <text x="900" y="160" text-anchor="middle" font-size="12" font-weight="bold" fill="white">NODE</text>

                        <circle cx="450" cy="250" r="40" fill="#4CAF50" stroke="#2E7D32" stroke-width="3"/>
                        <text x="450" y="245" text-anchor="middle" font-size="12" font-weight="bold" fill="white">FULL</text>
                        <text x="450" y="260" text-anchor="middle" font-size="12" font-weight="bold" fill="white">NODE</text>

                        <circle cx="750" cy="250" r="40" fill="#4CAF50" stroke="#2E7D32" stroke-width="3"/>
                        <text x="750" y="245" text-anchor="middle" font-size="12" font-weight="bold" fill="white">FULL</text>
                        <text x="750" y="260" text-anchor="middle" font-size="12" font-weight="bold" fill="white">NODE</text>

                        <!-- Mining Nodes -->
                        <rect x="550" y="300" width="100" height="60" fill="#FF9800" stroke="#F57C00" stroke-width="3" rx="10"/>
                        <text x="600" y="325" text-anchor="middle" font-size="12" font-weight="bold" fill="white">MINING</text>
                        <text x="600" y="340" text-anchor="middle" font-size="12" font-weight="bold" fill="white">NODE</text>
                        <text x="600" y="355" text-anchor="middle" font-size="10" fill="white">⛏️ Creates Blocks</text>

                        <!-- Light Nodes -->
                        <circle cx="150" cy="300" r="25" fill="#2196F3" stroke="#1976D2" stroke-width="2"/>
                        <text x="150" y="295" text-anchor="middle" font-size="10" font-weight="bold" fill="white">LIGHT</text>
                        <text x="150" y="308" text-anchor="middle" font-size="10" font-weight="bold" fill="white">NODE</text>

                        <circle cx="1050" cy="300" r="25" fill="#2196F3" stroke="#1976D2" stroke-width="2"/>
                        <text x="1050" y="295" text-anchor="middle" font-size="10" font-weight="bold" fill="white">LIGHT</text>
                        <text x="1050" y="308" text-anchor="middle" font-size="10" font-weight="bold" fill="white">NODE</text>

                        <!-- Network Connections -->
                        <line x1="340" y1="150" x2="560" y2="100" stroke="#666" stroke-width="2"/>
                        <line x1="640" y1="100" x2="860" y2="150" stroke="#666" stroke-width="2"/>
                        <line x1="300" y1="190" x2="450" y2="210" stroke="#666" stroke-width="2"/>
                        <line x1="490" y1="250" x2="710" y2="250" stroke="#666" stroke-width="2"/>
                        <line x1="750" y1="210" x2="900" y2="190" stroke="#666" stroke-width="2"/>
                        <line x1="600" y1="140" x2="600" y2="300" stroke="#666" stroke-width="2"/>
                        <line x1="450" y1="290" x2="550" y2="330" stroke="#666" stroke-width="2"/>
                        <line x1="650" y1="330" x2="750" y2="290" stroke="#666" stroke-width="2"/>

                        <!-- Light node connections -->
                        <line x1="175" y1="300" x2="260" y2="180" stroke="#2196F3" stroke-width="2" stroke-dasharray="5,5"/>
                        <line x1="1025" y1="300" x2="940" y2="180" stroke="#2196F3" stroke-width="2" stroke-dasharray="5,5"/>

                        <!-- Data flow indicators -->
                        <text x="600" y="400" text-anchor="middle" font-size="12" fill="#4CAF50" font-weight="bold">
                            ↔️ Continuous Data Synchronization
                        </text>

                        <text x="600" y="420" text-anchor="middle" font-size="11" fill="#666">
                            All nodes maintain identical copies of the blockchain
                        </text>
                    </g>

                    <!-- Network Statistics -->
                    <text x="100" y="460" font-size="12" font-weight="bold">Network Properties:</text>
                    <text x="100" y="480" font-size="11">• Fault Tolerant: Can lose up to 49% of nodes</text>
                    <text x="400" y="480" font-size="11">• Self-Healing: Automatically recovers from failures</text>
                    <text x="750" y="480" font-size="11">• Scalable: New nodes can join anytime</text>
                </svg>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">🔧</span>Node Types & Functions</h2>

            <div class="node-types-grid">
                <div class="node-card">
                    <h3>🟢 Full Nodes</h3>
                    <p><strong>Complete blockchain validators that store the entire blockchain history.</strong></p>
                    <ul>
                        <li>Store complete blockchain (100+ GB for Bitcoin)</li>
                        <li>Validate all transactions and blocks</li>
                        <li>Relay transactions to other nodes</li>
                        <li>Enforce network consensus rules</li>
                        <li>Provide security to the network</li>
                    </ul>
                    <div class="highlight">Essential for network decentralization</div>
                </div>

                <div class="node-card">
                    <h3>⛏️ Mining Nodes</h3>
                    <p><strong>Specialized nodes that create new blocks and secure the network.</strong></p>
                    <ul>
                        <li>Compete to solve cryptographic puzzles</li>
                        <li>Create new blocks with transactions</li>
                        <li>Receive block rewards and transaction fees</li>
                        <li>Require significant computational power</li>
                        <li>Usually run full node functionality</li>
                    </ul>
                    <div class="highlight">Network security providers</div>
                </div>

                <div class="node-card">
                    <h3>💡 Light Nodes (SPV)</h3>
                    <p><strong>Lightweight clients that don't store the full blockchain.</strong></p>
                    <ul>
                        <li>Store only block headers (~4MB for Bitcoin)</li>
                        <li>Verify transactions using Merkle proofs</li>
                        <li>Rely on full nodes for transaction data</li>
                        <li>Suitable for mobile devices and wallets</li>
                        <li>Fast synchronization and low storage</li>
                    </ul>
                    <div class="highlight">User-friendly blockchain access</div>
                </div>

                <div class="node-card">
                    <h3>🏪 Archive Nodes</h3>
                    <p><strong>Full nodes that store additional historical state data.</strong></p>
                    <ul>
                        <li>Store complete blockchain + all state history</li>
                        <li>Enable queries about past network states</li>
                        <li>Support blockchain explorers and analytics</li>
                        <li>Require massive storage (terabytes)</li>
                        <li>Essential for network transparency</li>
                    </ul>
                    <div class="highlight">Historical data preservation</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">📡</span>Communication Protocols</h2>

            <div class="protocol-stack">
                <div class="protocol-layer">
                    <h3>🌐 Network Layer (P2P Protocol)</h3>
                    <p><strong>Handles node discovery, connection management, and peer communication.</strong></p>
                    <ul>
                        <li>Node discovery through DNS seeds and peer exchange</li>
                        <li>Connection establishment and maintenance</li>
                        <li>Peer reputation and ban management</li>
                        <li>Network topology optimization</li>
                    </ul>
                </div>

                <div class="protocol-layer">
                    <h3>📦 Message Layer</h3>
                    <p><strong>Defines message formats and communication patterns.</strong></p>
                    <ul>
                        <li>Transaction propagation (inv/getdata/tx)</li>
                        <li>Block announcement and retrieval</li>
                        <li>Blockchain synchronization (headers/blocks)</li>
                        <li>Ping/pong for connection health</li>
                    </ul>
                </div>

                <div class="protocol-layer">
                    <h3>🔐 Consensus Layer</h3>
                    <p><strong>Ensures all nodes agree on the blockchain state.</strong></p>
                    <ul>
                        <li>Block validation and acceptance rules</li>
                        <li>Fork resolution mechanisms</li>
                        <li>Difficulty adjustment algorithms</li>
                        <li>Network upgrade coordination</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">📊</span>Network Statistics & Performance</h2>

            <div class="network-stats">
                <div class="stat-card">
                    <div class="stat-number">10,000+</div>
                    <div class="stat-label">Bitcoin Full Nodes</div>
                </div>

                <div class="stat-card">
                    <div class="stat-number">5,000+</div>
                    <div class="stat-label">Ethereum Nodes</div>
                </div>

                <div class="stat-card">
                    <div class="stat-number">~10 min</div>
                    <div class="stat-label">Bitcoin Block Time</div>
                </div>

                <div class="stat-card">
                    <div class="stat-number">~12 sec</div>
                    <div class="stat-label">Ethereum Block Time</div>
                </div>
            </div>

            <div class="concept-box">
                <h3>Network Health Indicators</h3>
                <p>A healthy blockchain network exhibits several key characteristics:</p>
                <ul>
                    <li><strong>Node Distribution:</strong> Geographically diverse node locations</li>
                    <li><strong>Network Hashrate:</strong> High computational power securing the network</li>
                    <li><strong>Transaction Throughput:</strong> Consistent processing of transactions</li>
                    <li><strong>Block Propagation:</strong> Fast distribution of new blocks</li>
                    <li><strong>Fork Resolution:</strong> Quick consensus on the canonical chain</li>
                </ul>

                <p><strong>Key Takeaway:</strong> Blockchain networks function as self-organizing, resilient systems where thousands of independent nodes work together to maintain a shared, immutable ledger without central coordination.</p>
            </div>
        </div>

    </div>
</body>
</html>
