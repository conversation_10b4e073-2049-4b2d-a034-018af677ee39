<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Question 5: Ethereum Blockchain Architecture</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #4facfe 100%);
            min-height: 100vh; line-height: 1.6; overflow-x: hidden;
        }
        .animated-bg {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: -1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #4facfe 100%);
            animation: gradientShift 25s ease infinite;
        }
        @keyframes gradientShift {
            0% { background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #4facfe 100%); }
            25% { background: linear-gradient(135deg, #f093fb 0%, #4facfe 30%, #667eea 60%, #764ba2 100%); }
            50% { background: linear-gradient(135deg, #4facfe 0%, #667eea 30%, #764ba2 60%, #f093fb 100%); }
            75% { background: linear-gradient(135deg, #764ba2 0%, #f093fb 30%, #4facfe 60%, #667eea 100%); }
            100% { background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #4facfe 100%); }
        }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header {
            text-align: center; margin-bottom: 40px; padding: 40px 20px;
            background: rgba(255, 255, 255, 0.95); border-radius: 25px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1); backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .question-number {
            font-size: 4em; font-weight: 900; margin-bottom: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb, #4facfe);
            -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;
        }
        .main-title { font-size: 2.8em; font-weight: 700; color: #2c3e50; margin-bottom: 15px; }
        .subtitle { font-size: 1.3em; color: #666; margin-bottom: 25px; }
        .section {
            margin: 40px 0; padding: 35px; background: rgba(255, 255, 255, 0.95);
            border-radius: 20px; box-shadow: 0 15px 45px rgba(0,0,0,0.1);
            backdrop-filter: blur(15px); border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        .section:hover { transform: translateY(-5px); box-shadow: 0 25px 60px rgba(0,0,0,0.15); }
        .section h2 {
            font-size: 2.2em; color: #2c3e50; margin-bottom: 25px; font-weight: 700;
            display: flex; align-items: center; gap: 15px;
        }
        .emoji { font-size: 1.2em; filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1)); }
        .concept-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 15px; padding: 30px; margin: 25px 0;
            border-left: 6px solid #2196f3; box-shadow: 0 8px 25px rgba(33, 150, 243, 0.1);
        }
        .highlight {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            padding: 4px 10px; border-radius: 6px; font-weight: 600;
            color: #856404; display: inline-block;
        }
        .diagram-container {
            background: rgba(248, 249, 250, 0.95); border: 2px solid rgba(222, 226, 230, 0.5);
            border-radius: 15px; padding: 25px; margin: 25px 0; overflow-x: auto; backdrop-filter: blur(10px);
        }
        .architecture-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 25px; margin: 30px 0; }
        .architecture-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
            border-radius: 15px; padding: 25px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease; border-top: 4px solid #627EEA;
        }
        .architecture-card:hover { transform: translateY(-8px); box-shadow: 0 20px 50px rgba(0,0,0,0.15); }
        .code-block {
            background: #2d3748; color: #e2e8f0; padding: 20px; border-radius: 10px;
            font-family: 'Courier New', monospace; margin: 15px 0; overflow-x: auto;
            border-left: 4px solid #627EEA;
        }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 25px 0; }
        .stat-card {
            background: linear-gradient(135deg, #627EEA, #4F46E5); color: white; padding: 20px;
            border-radius: 12px; text-align: center; box-shadow: 0 8px 25px rgba(98, 126, 234, 0.3);
            transition: all 0.3s ease;
        }
        .stat-card:hover { transform: translateY(-5px); box-shadow: 0 12px 35px rgba(98, 126, 234, 0.4); }
        .stat-number { font-size: 2.5em; font-weight: 900; margin-bottom: 10px; }
        .stat-label { font-size: 1.1em; opacity: 0.9; }
        @media (max-width: 768px) {
            .main-title { font-size: 2.2em; } .question-number { font-size: 3em; }
            .architecture-grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="animated-bg"></div>
    <div class="container">
        <div class="header">
            <div class="question-number">05</div>
            <h1 class="main-title">🔷 Ethereum Blockchain Architecture</h1>
            <p class="subtitle">EVM, State Management & Smart Contract Platform</p>
        </div>

        <div class="section">
            <h2><span class="emoji">🎯</span>Ethereum's World Computer Vision</h2>
            <div class="concept-box">
                <h3>Beyond Digital Currency to Programmable Blockchain</h3>
                <p><span class="highlight">Ethereum</span> represents a paradigm shift from Bitcoin's simple transaction ledger to a fully programmable blockchain platform. Designed as a "world computer," Ethereum enables developers to build decentralized applications (dApps) using smart contracts, creating a global, unstoppable computing platform.</p>

                <p><strong>Core Architectural Principles:</strong></p>
                <ul>
                    <li><strong>Turing Completeness:</strong> Support for any computational logic</li>
                    <li><strong>State Machine:</strong> Global state transitions through transactions</li>
                    <li><strong>Virtual Machine:</strong> Isolated execution environment (EVM)</li>
                    <li><strong>Gas Mechanism:</strong> Resource allocation and spam prevention</li>
                    <li><strong>Account Model:</strong> Two types of accounts with different capabilities</li>
                </ul>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">900K+</div>
                    <div class="stat-label">Active Validators</div>
                </div>

                <div class="stat-card">
                    <div class="stat-number">1M+</div>
                    <div class="stat-label">Smart Contracts</div>
                </div>

                <div class="stat-card">
                    <div class="stat-number">15</div>
                    <div class="stat-label">TPS Current Capacity</div>
                </div>

                <div class="stat-card">
                    <div class="stat-number">32M</div>
                    <div class="stat-label">ETH Staked</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">🏗️</span>Ethereum Virtual Machine (EVM)</h2>

            <div class="diagram-container">
                <svg width="100%" height="500" viewBox="0 0 1200 500">
                    <!-- Title -->
                    <text x="600" y="30" text-anchor="middle" font-size="20" font-weight="bold">ETHEREUM VIRTUAL MACHINE ARCHITECTURE</text>

                    <!-- EVM Core -->
                    <g id="evm-core" transform="translate(400, 80)">
                        <rect x="0" y="0" width="400" height="300" fill="#627EEA" stroke="#4F46E5" stroke-width="3" rx="15"/>
                        <text x="200" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="white">ETHEREUM VIRTUAL MACHINE</text>

                        <!-- Stack -->
                        <rect x="20" y="50" width="100" height="120" fill="#8B5CF6" stroke="#7C3AED" stroke-width="2" rx="8"/>
                        <text x="70" y="75" text-anchor="middle" font-size="12" font-weight="bold" fill="white">STACK</text>
                        <text x="70" y="95" text-anchor="middle" font-size="10" fill="white">1024 items max</text>
                        <text x="70" y="110" text-anchor="middle" font-size="10" fill="white">256-bit words</text>
                        <text x="70" y="125" text-anchor="middle" font-size="10" fill="white">LIFO operations</text>

                        <!-- Memory -->
                        <rect x="140" y="50" width="100" height="120" fill="#10B981" stroke="#059669" stroke-width="2" rx="8"/>
                        <text x="190" y="75" text-anchor="middle" font-size="12" font-weight="bold" fill="white">MEMORY</text>
                        <text x="190" y="95" text-anchor="middle" font-size="10" fill="white">Volatile</text>
                        <text x="190" y="110" text-anchor="middle" font-size="10" fill="white">Byte-addressable</text>
                        <text x="190" y="125" text-anchor="middle" font-size="10" fill="white">Gas cost grows</text>

                        <!-- Storage -->
                        <rect x="260" y="50" width="100" height="120" fill="#F59E0B" stroke="#D97706" stroke-width="2" rx="8"/>
                        <text x="310" y="75" text-anchor="middle" font-size="12" font-weight="bold" fill="white">STORAGE</text>
                        <text x="310" y="95" text-anchor="middle" font-size="10" fill="white">Persistent</text>
                        <text x="310" y="110" text-anchor="middle" font-size="10" fill="white">Key-value store</text>
                        <text x="310" y="125" text-anchor="middle" font-size="10" fill="white">Expensive</text>

                        <!-- Program Counter -->
                        <rect x="20" y="190" width="160" height="40" fill="#EF4444" stroke="#DC2626" stroke-width="2" rx="8"/>
                        <text x="100" y="215" text-anchor="middle" font-size="12" font-weight="bold" fill="white">PROGRAM COUNTER</text>

                        <!-- Gas -->
                        <rect x="200" y="190" width="160" height="40" fill="#6366F1" stroke="#4F46E5" stroke-width="2" rx="8"/>
                        <text x="280" y="215" text-anchor="middle" font-size="12" font-weight="bold" fill="white">GAS METER</text>

                        <!-- Execution Environment -->
                        <rect x="20" y="250" width="340" height="40" fill="#1F2937" stroke="#374151" stroke-width="2" rx="8"/>
                        <text x="190" y="275" text-anchor="middle" font-size="12" font-weight="bold" fill="white">EXECUTION ENVIRONMENT</text>
                    </g>

                    <!-- Bytecode Input -->
                    <g id="bytecode" transform="translate(50, 150)">
                        <rect x="0" y="0" width="300" height="80" fill="#F3F4F6" stroke="#D1D5DB" stroke-width="2" rx="10"/>
                        <text x="150" y="25" text-anchor="middle" font-size="14" font-weight="bold">SMART CONTRACT BYTECODE</text>
                        <text x="150" y="45" text-anchor="middle" font-size="11">0x608060405234801561001057600080fd5b50...</text>
                        <text x="150" y="65" text-anchor="middle" font-size="10">Compiled Solidity → EVM Opcodes</text>
                    </g>

                    <!-- State Output -->
                    <g id="state" transform="translate(850, 150)">
                        <rect x="0" y="0" width="300" height="80" fill="#F3F4F6" stroke="#D1D5DB" stroke-width="2" rx="10"/>
                        <text x="150" y="25" text-anchor="middle" font-size="14" font-weight="bold">WORLD STATE CHANGES</text>
                        <text x="150" y="45" text-anchor="middle" font-size="11">Account balances, storage updates</text>
                        <text x="150" y="65" text-anchor="middle" font-size="10">Merkle Patricia Trie</text>
                    </g>

                    <!-- Arrows -->
                    <path d="M 350 190 L 400 190" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                    <path d="M 800 190 L 850 190" stroke="#374151" stroke-width="3" fill="none" marker-end="url(#arrow)"/>

                    <!-- Opcodes -->
                    <g id="opcodes" transform="translate(100, 350)">
                        <text x="500" y="20" text-anchor="middle" font-size="16" font-weight="bold">EVM OPCODES EXAMPLES</text>

                        <rect x="0" y="30" width="120" height="60" fill="#8B5CF6" stroke="#7C3AED" stroke-width="2" rx="5"/>
                        <text x="60" y="50" text-anchor="middle" font-size="11" font-weight="bold" fill="white">ARITHMETIC</text>
                        <text x="60" y="70" text-anchor="middle" font-size="9" fill="white">ADD, SUB, MUL</text>

                        <rect x="140" y="30" width="120" height="60" fill="#10B981" stroke="#059669" stroke-width="2" rx="5"/>
                        <text x="200" y="50" text-anchor="middle" font-size="11" font-weight="bold" fill="white">COMPARISON</text>
                        <text x="200" y="70" text-anchor="middle" font-size="9" fill="white">LT, GT, EQ</text>

                        <rect x="280" y="30" width="120" height="60" fill="#F59E0B" stroke="#D97706" stroke-width="2" rx="5"/>
                        <text x="340" y="50" text-anchor="middle" font-size="11" font-weight="bold" fill="white">STORAGE</text>
                        <text x="340" y="70" text-anchor="middle" font-size="9" fill="white">SLOAD, SSTORE</text>

                        <rect x="420" y="30" width="120" height="60" fill="#EF4444" stroke="#DC2626" stroke-width="2" rx="5"/>
                        <text x="480" y="50" text-anchor="middle" font-size="11" font-weight="bold" fill="white">CONTROL</text>
                        <text x="480" y="70" text-anchor="middle" font-size="9" fill="white">JUMP, JUMPI</text>

                        <rect x="560" y="30" width="120" height="60" fill="#6366F1" stroke="#4F46E5" stroke-width="2" rx="5"/>
                        <text x="620" y="50" text-anchor="middle" font-size="11" font-weight="bold" fill="white">SYSTEM</text>
                        <text x="620" y="70" text-anchor="middle" font-size="9" fill="white">CALL, CREATE</text>

                        <rect x="700" y="30" width="120" height="60" fill="#8B5CF6" stroke="#7C3AED" stroke-width="2" rx="5"/>
                        <text x="760" y="50" text-anchor="middle" font-size="11" font-weight="bold" fill="white">CRYPTO</text>
                        <text x="760" y="70" text-anchor="middle" font-size="9" fill="white">SHA3, ECRECOVER</text>

                        <rect x="840" y="30" width="120" height="60" fill="#10B981" stroke="#059669" stroke-width="2" rx="5"/>
                        <text x="900" y="50" text-anchor="middle" font-size="11" font-weight="bold" fill="white">ENVIRONMENT</text>
                        <text x="900" y="70" text-anchor="middle" font-size="9" fill="white">ADDRESS, BALANCE</text>
                    </g>

                    <!-- Arrow marker definition -->
                    <defs>
                        <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#374151"/>
                        </marker>
                    </defs>
                </svg>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">🏦</span>Account Model & State Management</h2>
            <div class="architecture-grid">
                <div class="architecture-card">
                    <h3>👤 Externally Owned Accounts (EOAs)</h3>
                    <p><strong>User-controlled accounts with private keys</strong></p>
                    <ul>
                        <li><strong>Private Key:</strong> 256-bit cryptographic key</li>
                        <li><strong>Address:</strong> Last 20 bytes of Keccak-256(public key)</li>
                        <li><strong>Balance:</strong> ETH balance in wei</li>
                        <li><strong>Nonce:</strong> Transaction counter for replay protection</li>
                        <li><strong>Capabilities:</strong> Send transactions, call contracts</li>
                        <li><strong>No Code:</strong> Cannot execute smart contract logic</li>
                    </ul>
                </div>

                <div class="architecture-card">
                    <h3>🤖 Contract Accounts</h3>
                    <p><strong>Smart contracts with executable code</strong></p>
                    <ul>
                        <li><strong>Address:</strong> Deterministic from creator + nonce</li>
                        <li><strong>Balance:</strong> Can hold and receive ETH</li>
                        <li><strong>Code:</strong> Immutable bytecode after deployment</li>
                        <li><strong>Storage:</strong> Persistent key-value storage</li>
                        <li><strong>Nonce:</strong> Tracks created contracts</li>
                        <li><strong>Execution:</strong> Triggered by transactions or calls</li>
                    </ul>
                </div>

                <div class="architecture-card">
                    <h3>🌳 World State Trie</h3>
                    <p><strong>Global state storage using Merkle Patricia Trie</strong></p>
                    <ul>
                        <li><strong>Structure:</strong> Modified Merkle Patricia Trie</li>
                        <li><strong>Root Hash:</strong> Single hash representing entire state</li>
                        <li><strong>Efficiency:</strong> Only changed nodes need updating</li>
                        <li><strong>Verification:</strong> Cryptographic proof of state</li>
                        <li><strong>Storage:</strong> Account data and contract storage</li>
                        <li><strong>Synchronization:</strong> Enables light client verification</li>
                    </ul>
                </div>

                <div class="architecture-card">
                    <h3>⛽ Gas System</h3>
                    <p><strong>Resource allocation and execution cost mechanism</strong></p>
                    <ul>
                        <li><strong>Gas Limit:</strong> Maximum gas per transaction/block</li>
                        <li><strong>Gas Price:</strong> Wei per gas unit (pre-EIP-1559)</li>
                        <li><strong>Gas Used:</strong> Actual computational resources consumed</li>
                        <li><strong>Refund:</strong> Unused gas returned to sender</li>
                        <li><strong>Out of Gas:</strong> Execution halts, state reverted</li>
                        <li><strong>EIP-1559:</strong> Base fee + priority fee model</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">💻</span>Smart Contract Execution</h2>
            <div class="code-block">
// Solidity Smart Contract Example
pragma solidity ^0.8.0;

contract SimpleStorage {
    uint256 private storedData;

    event DataStored(uint256 indexed value, address indexed setter);

    function set(uint256 x) public {
        storedData = x;
        emit DataStored(x, msg.sender);
    }

    function get() public view returns (uint256) {
        return storedData;
    }
}

// Compiled to EVM Bytecode:
// 608060405234801561001057600080fd5b50...
            </div>

            <div class="concept-box">
                <h3>Smart Contract Lifecycle</h3>
                <p><strong>From development to execution on Ethereum:</strong></p>

                <p><strong>Development Phase:</strong></p>
                <ul>
                    <li><strong>Solidity Code:</strong> High-level programming language</li>
                    <li><strong>Compilation:</strong> Solidity → EVM bytecode</li>
                    <li><strong>Testing:</strong> Local blockchain simulation</li>
                    <li><strong>Optimization:</strong> Gas usage optimization</li>
                </ul>

                <p><strong>Deployment Phase:</strong></p>
                <ul>
                    <li><strong>Transaction:</strong> Special transaction with bytecode</li>
                    <li><strong>Address Generation:</strong> Deterministic contract address</li>
                    <li><strong>State Initialization:</strong> Constructor execution</li>
                    <li><strong>Code Storage:</strong> Bytecode stored on blockchain</li>
                </ul>

                <p><strong>Execution Phase:</strong></p>
                <ul>
                    <li><strong>Function Calls:</strong> External transactions or internal calls</li>
                    <li><strong>EVM Execution:</strong> Bytecode interpretation</li>
                    <li><strong>State Changes:</strong> Storage and balance updates</li>
                    <li><strong>Event Emission:</strong> Logs for external monitoring</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">🔄</span>Ethereum 2.0 Transition</h2>
            <div class="concept-box">
                <h3>The Merge: From PoW to PoS</h3>
                <p>Ethereum's transition to Proof of Stake represents one of the most significant upgrades in blockchain history:</p>

                <p><strong>Key Changes:</strong></p>
                <ul>
                    <li><strong>Consensus Mechanism:</strong> Proof of Work → Proof of Stake</li>
                    <li><strong>Energy Reduction:</strong> 99.95% decrease in energy consumption</li>
                    <li><strong>Validator System:</strong> 32 ETH minimum stake requirement</li>
                    <li><strong>Finality:</strong> Faster transaction finality (12.8 minutes)</li>
                    <li><strong>Issuance Reduction:</strong> Lower ETH inflation rate</li>
                    <li><strong>Sharding Preparation:</strong> Foundation for future scaling</li>
                </ul>

                <p><strong>Future Roadmap:</strong></p>
                <ul>
                    <li><strong>Sharding:</strong> 64 shard chains for parallel processing</li>
                    <li><strong>eWASM:</strong> WebAssembly virtual machine</li>
                    <li><strong>Statelessness:</strong> Reduced node storage requirements</li>
                    <li><strong>Quantum Resistance:</strong> Post-quantum cryptography</li>
                </ul>

                <p><strong>Key Takeaway:</strong> Ethereum's architecture represents the most sophisticated blockchain platform, combining a Turing-complete virtual machine with a robust state management system. The transition to Proof of Stake and planned sharding upgrades position Ethereum as the foundation for a decentralized internet, supporting everything from simple transfers to complex DeFi protocols and DAOs.</p>
            </div>
        </div>

    </div>
</body>
</html>