<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Question 7: Smart Contracts & Applications</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #4facfe 100%);
            min-height: 100vh; line-height: 1.6; overflow-x: hidden;
        }
        .animated-bg {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: -1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #4facfe 100%);
            animation: gradientShift 25s ease infinite;
        }
        @keyframes gradientShift {
            0% { background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #4facfe 100%); }
            25% { background: linear-gradient(135deg, #f093fb 0%, #4facfe 30%, #667eea 60%, #764ba2 100%); }
            50% { background: linear-gradient(135deg, #4facfe 0%, #667eea 30%, #764ba2 60%, #f093fb 100%); }
            75% { background: linear-gradient(135deg, #764ba2 0%, #f093fb 30%, #4facfe 60%, #667eea 100%); }
            100% { background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #4facfe 100%); }
        }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header {
            text-align: center; margin-bottom: 40px; padding: 40px 20px;
            background: rgba(255, 255, 255, 0.95); border-radius: 25px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1); backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .question-number {
            font-size: 4em; font-weight: 900; margin-bottom: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb, #4facfe);
            -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;
        }
        .main-title { font-size: 2.8em; font-weight: 700; color: #2c3e50; margin-bottom: 15px; }
        .subtitle { font-size: 1.3em; color: #666; margin-bottom: 25px; }
        .section {
            margin: 40px 0; padding: 35px; background: rgba(255, 255, 255, 0.95);
            border-radius: 20px; box-shadow: 0 15px 45px rgba(0,0,0,0.1);
            backdrop-filter: blur(15px); border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        .section:hover { transform: translateY(-5px); box-shadow: 0 25px 60px rgba(0,0,0,0.15); }
        .section h2 {
            font-size: 2.2em; color: #2c3e50; margin-bottom: 25px; font-weight: 700;
            display: flex; align-items: center; gap: 15px;
        }
        .emoji { font-size: 1.2em; filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1)); }
        .concept-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 15px; padding: 30px; margin: 25px 0;
            border-left: 6px solid #2196f3; box-shadow: 0 8px 25px rgba(33, 150, 243, 0.1);
        }
        .highlight {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            padding: 4px 10px; border-radius: 6px; font-weight: 600;
            color: #856404; display: inline-block;
        }
        .code-block {
            background: #2d3748; color: #e2e8f0; padding: 20px; border-radius: 10px;
            font-family: 'Courier New', monospace; margin: 15px 0; overflow-x: auto;
            border-left: 4px solid #667eea;
        }
        .applications-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 25px; margin: 30px 0; }
        .application-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
            border-radius: 15px; padding: 25px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease; border-top: 4px solid #4CAF50;
        }
        .application-card:hover { transform: translateY(-8px); box-shadow: 0 20px 50px rgba(0,0,0,0.15); }
        .defi-card { border-top: 4px solid #FF9800; }
        .security-card { border-top: 4px solid #F44336; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 25px 0; }
        .stat-card {
            background: linear-gradient(135deg, #4CAF50, #388E3C); color: white; padding: 20px;
            border-radius: 12px; text-align: center; box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
            transition: all 0.3s ease;
        }
        .stat-card:hover { transform: translateY(-5px); box-shadow: 0 12px 35px rgba(76, 175, 80, 0.4); }
        .stat-number { font-size: 2.5em; font-weight: 900; margin-bottom: 10px; }
        .stat-label { font-size: 1.1em; opacity: 0.9; }
        @media (max-width: 768px) {
            .main-title { font-size: 2.2em; } .question-number { font-size: 3em; }
            .applications-grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="animated-bg"></div>
    <div class="container">
        <div class="header">
            <div class="question-number">07</div>
            <h1 class="main-title">🤖 Smart Contracts & Applications</h1>
            <p class="subtitle">Solidity Programming, DeFi Protocols & Contract Security</p>
        </div>

        <div class="section">
            <h2><span class="emoji">🎯</span>Understanding Smart Contracts</h2>
            <div class="concept-box">
                <h3>Self-Executing Code on Blockchain</h3>
                <p><span class="highlight">Smart contracts</span> are self-executing programs stored on blockchain that automatically enforce agreements when predetermined conditions are met. They eliminate the need for intermediaries and enable trustless interactions between parties.</p>
                
                <p><strong>Key Characteristics:</strong></p>
                <ul>
                    <li><strong>Immutable:</strong> Code cannot be changed after deployment</li>
                    <li><strong>Deterministic:</strong> Same inputs always produce same outputs</li>
                    <li><strong>Transparent:</strong> Code is publicly visible and verifiable</li>
                    <li><strong>Autonomous:</strong> Execute automatically without human intervention</li>
                    <li><strong>Trustless:</strong> No need to trust counterparties</li>
                </ul>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">1M+</div>
                    <div class="stat-label">Smart Contracts</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number">$200B+</div>
                    <div class="stat-label">DeFi TVL</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number">3000+</div>
                    <div class="stat-label">DeFi Protocols</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number">50M+</div>
                    <div class="stat-label">Contract Interactions</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">💻</span>Solidity Programming</h2>
            <div class="code-block">
// ERC-20 Token Contract Example
pragma solidity ^0.8.0;

contract MyToken {
    string public name = "MyToken";
    string public symbol = "MTK";
    uint8 public decimals = 18;
    uint256 public totalSupply = 1000000 * 10**18;
    
    mapping(address => uint256) public balanceOf;
    mapping(address => mapping(address => uint256)) public allowance;
    
    event Transfer(address indexed from, address indexed to, uint256 value);
    event Approval(address indexed owner, address indexed spender, uint256 value);
    
    constructor() {
        balanceOf[msg.sender] = totalSupply;
    }
    
    function transfer(address to, uint256 value) public returns (bool) {
        require(balanceOf[msg.sender] >= value, "Insufficient balance");
        balanceOf[msg.sender] -= value;
        balanceOf[to] += value;
        emit Transfer(msg.sender, to, value);
        return true;
    }
    
    function approve(address spender, uint256 value) public returns (bool) {
        allowance[msg.sender][spender] = value;
        emit Approval(msg.sender, spender, value);
        return true;
    }
}
            </div>
            
            <div class="concept-box">
                <h3>Solidity Language Features</h3>
                <p><strong>Key programming concepts for smart contract development:</strong></p>
                
                <p><strong>Data Types:</strong></p>
                <ul>
                    <li><strong>uint/int:</strong> Unsigned/signed integers (8 to 256 bits)</li>
                    <li><strong>address:</strong> 20-byte Ethereum addresses</li>
                    <li><strong>bool:</strong> Boolean true/false values</li>
                    <li><strong>bytes:</strong> Fixed or dynamic byte arrays</li>
                    <li><strong>string:</strong> UTF-8 encoded strings</li>
                    <li><strong>mapping:</strong> Hash table key-value storage</li>
                </ul>
                
                <p><strong>Special Variables:</strong></p>
                <ul>
                    <li><strong>msg.sender:</strong> Address calling the function</li>
                    <li><strong>msg.value:</strong> ETH sent with transaction</li>
                    <li><strong>block.timestamp:</strong> Current block timestamp</li>
                    <li><strong>block.number:</strong> Current block number</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">🏦</span>DeFi Applications</h2>
            <div class="applications-grid">
                <div class="application-card defi-card">
                    <h3>🔄 Automated Market Makers (AMMs)</h3>
                    <p><strong>Decentralized trading without order books</strong></p>
                    <ul>
                        <li><strong>Uniswap:</strong> x*y=k constant product formula</li>
                        <li><strong>Liquidity Pools:</strong> User-provided trading liquidity</li>
                        <li><strong>Price Discovery:</strong> Algorithmic price determination</li>
                        <li><strong>Fees:</strong> 0.3% trading fees to liquidity providers</li>
                        <li><strong>Slippage:</strong> Price impact from large trades</li>
                        <li><strong>Impermanent Loss:</strong> Risk for liquidity providers</li>
                    </ul>
                </div>

                <div class="application-card defi-card">
                    <h3>💰 Lending Protocols</h3>
                    <p><strong>Decentralized borrowing and lending</strong></p>
                    <ul>
                        <li><strong>Compound:</strong> Algorithmic interest rate protocol</li>
                        <li><strong>Aave:</strong> Flash loans and variable rates</li>
                        <li><strong>Collateralization:</strong> Over-collateralized loans</li>
                        <li><strong>Liquidation:</strong> Automatic collateral seizure</li>
                        <li><strong>Interest Rates:</strong> Supply/demand based rates</li>
                        <li><strong>Governance Tokens:</strong> COMP, AAVE for protocol control</li>
                    </ul>
                </div>

                <div class="application-card defi-card">
                    <h3>🏛️ Synthetic Assets</h3>
                    <p><strong>Blockchain-based derivatives and synthetics</strong></p>
                    <ul>
                        <li><strong>Synthetix:</strong> Synthetic stocks, commodities, currencies</li>
                        <li><strong>Mirror Protocol:</strong> Synthetic US stocks</li>
                        <li><strong>Collateral Backing:</strong> SNX tokens as collateral</li>
                        <li><strong>Oracle Feeds:</strong> Real-world price data</li>
                        <li><strong>Global Access:</strong> 24/7 trading of traditional assets</li>
                        <li><strong>No Custody:</strong> No need to hold underlying assets</li>
                    </ul>
                </div>

                <div class="application-card defi-card">
                    <h3>🛡️ Insurance Protocols</h3>
                    <p><strong>Decentralized risk coverage</strong></p>
                    <ul>
                        <li><strong>Nexus Mutual:</strong> Smart contract cover</li>
                        <li><strong>Cover Protocol:</strong> DeFi insurance marketplace</li>
                        <li><strong>Risk Assessment:</strong> Community-driven underwriting</li>
                        <li><strong>Claims Process:</strong> Decentralized claims handling</li>
                        <li><strong>Capital Pool:</strong> Shared risk pool model</li>
                        <li><strong>Staking:</strong> Risk assessors stake tokens</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">🔒</span>Security Best Practices</h2>
            <div class="applications-grid">
                <div class="application-card security-card">
                    <h3>⚠️ Common Vulnerabilities</h3>
                    <ul>
                        <li><strong>Reentrancy:</strong> Recursive calls draining funds</li>
                        <li><strong>Integer Overflow:</strong> Arithmetic wraparound bugs</li>
                        <li><strong>Access Control:</strong> Unauthorized function access</li>
                        <li><strong>Front-running:</strong> MEV extraction attacks</li>
                        <li><strong>Oracle Manipulation:</strong> Price feed attacks</li>
                        <li><strong>Flash Loan Attacks:</strong> Temporary capital for exploits</li>
                    </ul>
                </div>

                <div class="application-card security-card">
                    <h3>🛡️ Security Measures</h3>
                    <ul>
                        <li><strong>Checks-Effects-Interactions:</strong> Safe function ordering</li>
                        <li><strong>ReentrancyGuard:</strong> Modifier preventing reentrancy</li>
                        <li><strong>SafeMath:</strong> Overflow protection (pre-0.8.0)</li>
                        <li><strong>Access Control:</strong> Role-based permissions</li>
                        <li><strong>Time Locks:</strong> Delayed execution for upgrades</li>
                        <li><strong>Multi-sig:</strong> Multiple signatures for critical functions</li>
                    </ul>
                </div>

                <div class="application-card security-card">
                    <h3>🔍 Audit Process</h3>
                    <ul>
                        <li><strong>Code Review:</strong> Line-by-line security analysis</li>
                        <li><strong>Automated Tools:</strong> Slither, MythX, Securify</li>
                        <li><strong>Formal Verification:</strong> Mathematical proof of correctness</li>
                        <li><strong>Bug Bounties:</strong> Incentivized vulnerability discovery</li>
                        <li><strong>Test Coverage:</strong> Comprehensive unit testing</li>
                        <li><strong>Continuous Monitoring:</strong> Runtime security monitoring</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">🔮</span>Future of Smart Contracts</h2>
            <div class="concept-box">
                <h3>Evolution and Innovation</h3>
                <p>Smart contracts continue to evolve with new capabilities and applications:</p>
                
                <p><strong>Technical Innovations:</strong></p>
                <ul>
                    <li><strong>Account Abstraction:</strong> Programmable wallet logic</li>
                    <li><strong>Zero-Knowledge Proofs:</strong> Private smart contracts</li>
                    <li><strong>Cross-Chain Contracts:</strong> Multi-blockchain execution</li>
                    <li><strong>Upgradeable Contracts:</strong> Proxy patterns for updates</li>
                </ul>
                
                <p><strong>New Applications:</strong></p>
                <ul>
                    <li><strong>Real Estate:</strong> Property tokenization and trading</li>
                    <li><strong>Supply Chain:</strong> Transparent tracking and verification</li>
                    <li><strong>Identity:</strong> Self-sovereign identity management</li>
                    <li><strong>Gaming:</strong> NFTs and play-to-earn economies</li>
                </ul>
                
                <p><strong>Key Takeaway:</strong> Smart contracts have revolutionized how we think about agreements and automation. From simple token transfers to complex DeFi protocols, they enable trustless interactions at scale. The future will see even more sophisticated applications as the technology matures and security practices improve.</p>
            </div>
        </div>

    </div>
</body>
</html>
