<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Question 8: GHOST Protocol</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #4facfe 100%);
            min-height: 100vh; line-height: 1.6; overflow-x: hidden;
        }
        .animated-bg {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: -1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #4facfe 100%);
            animation: gradientShift 25s ease infinite;
        }
        @keyframes gradientShift {
            0% { background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #4facfe 100%); }
            25% { background: linear-gradient(135deg, #f093fb 0%, #4facfe 30%, #667eea 60%, #764ba2 100%); }
            50% { background: linear-gradient(135deg, #4facfe 0%, #667eea 30%, #764ba2 60%, #f093fb 100%); }
            75% { background: linear-gradient(135deg, #764ba2 0%, #f093fb 30%, #4facfe 60%, #667eea 100%); }
            100% { background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #4facfe 100%); }
        }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header {
            text-align: center; margin-bottom: 40px; padding: 40px 20px;
            background: rgba(255, 255, 255, 0.95); border-radius: 25px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1); backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .question-number {
            font-size: 4em; font-weight: 900; margin-bottom: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb, #4facfe);
            -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;
        }
        .main-title { font-size: 2.8em; font-weight: 700; color: #2c3e50; margin-bottom: 15px; }
        .subtitle { font-size: 1.3em; color: #666; margin-bottom: 25px; }
        .section {
            margin: 40px 0; padding: 35px; background: rgba(255, 255, 255, 0.95);
            border-radius: 20px; box-shadow: 0 15px 45px rgba(0,0,0,0.1);
            backdrop-filter: blur(15px); border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        .section:hover { transform: translateY(-5px); box-shadow: 0 25px 60px rgba(0,0,0,0.15); }
        .section h2 {
            font-size: 2.2em; color: #2c3e50; margin-bottom: 25px; font-weight: 700;
            display: flex; align-items: center; gap: 15px;
        }
        .emoji { font-size: 1.2em; filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1)); }
        .concept-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 15px; padding: 30px; margin: 25px 0;
            border-left: 6px solid #2196f3; box-shadow: 0 8px 25px rgba(33, 150, 243, 0.1);
        }
        .highlight {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            padding: 4px 10px; border-radius: 6px; font-weight: 600;
            color: #856404; display: inline-block;
        }
        .diagram-container {
            background: rgba(248, 249, 250, 0.95); border: 2px solid rgba(222, 226, 230, 0.5);
            border-radius: 15px; padding: 25px; margin: 25px 0; overflow-x: auto; backdrop-filter: blur(10px);
        }
        .ghost-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 25px; margin: 30px 0; }
        .ghost-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
            border-radius: 15px; padding: 25px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease; border-top: 4px solid #9C27B0;
        }
        .ghost-card:hover { transform: translateY(-8px); box-shadow: 0 20px 50px rgba(0,0,0,0.15); }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 25px 0; }
        .stat-card {
            background: linear-gradient(135deg, #9C27B0, #7B1FA2); color: white; padding: 20px;
            border-radius: 12px; text-align: center; box-shadow: 0 8px 25px rgba(156, 39, 176, 0.3);
            transition: all 0.3s ease;
        }
        .stat-card:hover { transform: translateY(-5px); box-shadow: 0 12px 35px rgba(156, 39, 176, 0.4); }
        .stat-number { font-size: 2.5em; font-weight: 900; margin-bottom: 10px; }
        .stat-label { font-size: 1.1em; opacity: 0.9; }
        @media (max-width: 768px) {
            .main-title { font-size: 2.2em; } .question-number { font-size: 3em; }
            .ghost-grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="animated-bg"></div>
    <div class="container">
        <div class="header">
            <div class="question-number">08</div>
            <h1 class="main-title">👻 GHOST Protocol</h1>
            <p class="subtitle">Greedy Heaviest Observed Subtree & Blockchain Efficiency</p>
        </div>

        <div class="section">
            <h2><span class="emoji">🎯</span>Understanding GHOST</h2>
            <div class="concept-box">
                <h3>Greedy Heaviest Observed Subtree Protocol</h3>
                <p>The <span class="highlight">GHOST protocol</span> (Greedy Heaviest Observed Subtree) is a blockchain consensus mechanism that improves upon Bitcoin's longest chain rule by considering the weight of entire subtrees rather than just the main chain. This allows for faster block times while maintaining security.</p>
                
                <p><strong>Core Innovation:</strong></p>
                <ul>
                    <li><strong>Subtree Weight:</strong> Consider all blocks in subtree, not just main chain</li>
                    <li><strong>Uncle Inclusion:</strong> Reference orphaned blocks for additional security</li>
                    <li><strong>Faster Block Times:</strong> Reduce confirmation delays</li>
                    <li><strong>Higher Throughput:</strong> More transactions per unit time</li>
                    <li><strong>Maintained Security:</strong> Preserve decentralization and security</li>
                </ul>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">12s</div>
                    <div class="stat-label">Ethereum Block Time</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number">7</div>
                    <div class="stat-label">Uncle Depth Limit</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number">2</div>
                    <div class="stat-label">Max Uncles per Block</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number">87.5%</div>
                    <div class="stat-label">Uncle Reward Rate</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">🌳</span>GHOST vs Longest Chain</h2>
            
            <div class="diagram-container">
                <svg width="100%" height="600" viewBox="0 0 1200 600">
                    <!-- Title -->
                    <text x="600" y="30" text-anchor="middle" font-size="20" font-weight="bold">GHOST PROTOCOL vs LONGEST CHAIN</text>
                    
                    <!-- Bitcoin Longest Chain -->
                    <g id="bitcoin-chain" transform="translate(50, 80)">
                        <text x="250" y="20" text-anchor="middle" font-size="16" font-weight="bold">BITCOIN LONGEST CHAIN RULE</text>
                        
                        <!-- Main chain -->
                        <rect x="0" y="40" width="80" height="50" fill="#FF9800" stroke="#F57C00" stroke-width="2" rx="5"/>
                        <text x="40" y="70" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Block 1</text>
                        
                        <rect x="120" y="40" width="80" height="50" fill="#FF9800" stroke="#F57C00" stroke-width="2" rx="5"/>
                        <text x="160" y="70" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Block 2</text>
                        
                        <rect x="240" y="40" width="80" height="50" fill="#FF9800" stroke="#F57C00" stroke-width="2" rx="5"/>
                        <text x="280" y="70" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Block 3</text>
                        
                        <rect x="360" y="40" width="80" height="50" fill="#FF9800" stroke="#F57C00" stroke-width="2" rx="5"/>
                        <text x="400" y="70" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Block 4</text>
                        
                        <!-- Orphaned blocks -->
                        <rect x="120" y="120" width="80" height="50" fill="#F44336" stroke="#D32F2F" stroke-width="2" rx="5"/>
                        <text x="160" y="145" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Orphan</text>
                        <text x="160" y="160" text-anchor="middle" font-size="9" fill="white">Discarded</text>
                        
                        <rect x="240" y="120" width="80" height="50" fill="#F44336" stroke="#D32F2F" stroke-width="2" rx="5"/>
                        <text x="280" y="145" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Orphan</text>
                        <text x="280" y="160" text-anchor="middle" font-size="9" fill="white">Discarded</text>
                        
                        <!-- Arrows -->
                        <path d="M 80 65 L 120 65" stroke="#F57C00" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                        <path d="M 200 65 L 240 65" stroke="#F57C00" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                        <path d="M 320 65 L 360 65" stroke="#F57C00" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                        
                        <!-- Orphan connections -->
                        <line x1="160" y1="90" x2="160" y2="120" stroke="#F44336" stroke-width="2" stroke-dasharray="3,3"/>
                        <line x1="280" y1="90" x2="280" y2="120" stroke="#F44336" stroke-width="2" stroke-dasharray="3,3"/>
                        
                        <text x="250" y="200" text-anchor="middle" font-size="12">Only main chain counts for weight</text>
                    </g>
                    
                    <!-- GHOST Protocol -->
                    <g id="ghost-chain" transform="translate(650, 80)">
                        <text x="250" y="20" text-anchor="middle" font-size="16" font-weight="bold">GHOST PROTOCOL</text>
                        
                        <!-- Main chain -->
                        <rect x="0" y="40" width="80" height="50" fill="#9C27B0" stroke="#7B1FA2" stroke-width="2" rx="5"/>
                        <text x="40" y="70" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Block 1</text>
                        
                        <rect x="120" y="40" width="80" height="50" fill="#9C27B0" stroke="#7B1FA2" stroke-width="2" rx="5"/>
                        <text x="160" y="70" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Block 2</text>
                        
                        <rect x="240" y="40" width="80" height="50" fill="#9C27B0" stroke="#7B1FA2" stroke-width="2" rx="5"/>
                        <text x="280" y="70" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Block 3</text>
                        
                        <rect x="360" y="40" width="80" height="50" fill="#9C27B0" stroke="#7B1FA2" stroke-width="2" rx="5"/>
                        <text x="400" y="70" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Block 4</text>
                        
                        <!-- Uncle blocks -->
                        <rect x="120" y="120" width="80" height="50" fill="#4CAF50" stroke="#388E3C" stroke-width="2" rx="5"/>
                        <text x="160" y="145" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Uncle</text>
                        <text x="160" y="160" text-anchor="middle" font-size="9" fill="white">Included</text>
                        
                        <rect x="240" y="120" width="80" height="50" fill="#4CAF50" stroke="#388E3C" stroke-width="2" rx="5"/>
                        <text x="280" y="145" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Uncle</text>
                        <text x="280" y="160" text-anchor="middle" font-size="9" fill="white">Included</text>
                        
                        <!-- Arrows -->
                        <path d="M 80 65 L 120 65" stroke="#7B1FA2" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                        <path d="M 200 65 L 240 65" stroke="#7B1FA2" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                        <path d="M 320 65 L 360 65" stroke="#7B1FA2" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                        
                        <!-- Uncle references -->
                        <line x1="160" y1="90" x2="160" y2="120" stroke="#4CAF50" stroke-width="2"/>
                        <line x1="280" y1="90" x2="280" y2="120" stroke="#4CAF50" stroke-width="2"/>
                        
                        <text x="250" y="200" text-anchor="middle" font-size="12">All blocks contribute to subtree weight</text>
                    </g>
                    
                    <!-- Algorithm Comparison -->
                    <g id="algorithm" transform="translate(100, 350)">
                        <text x="500" y="20" text-anchor="middle" font-size="18" font-weight="bold">CHAIN SELECTION ALGORITHMS</text>
                        
                        <rect x="0" y="40" width="450" height="120" fill="#FFF3E0" stroke="#FF9800" stroke-width="2" rx="10"/>
                        <text x="225" y="65" text-anchor="middle" font-size="14" font-weight="bold">LONGEST CHAIN RULE</text>
                        <text x="225" y="90" text-anchor="middle" font-size="12">Chain Weight = Number of blocks in main chain</text>
                        <text x="225" y="110" text-anchor="middle" font-size="12">Select chain with most blocks</text>
                        <text x="225" y="130" text-anchor="middle" font-size="12">Orphaned blocks provide no security</text>
                        <text x="225" y="150" text-anchor="middle" font-size="12">Slower block times for security</text>
                        
                        <rect x="500" y="40" width="450" height="120" fill="#F3E5F5" stroke="#9C27B0" stroke-width="2" rx="10"/>
                        <text x="725" y="65" text-anchor="middle" font-size="14" font-weight="bold">GHOST PROTOCOL</text>
                        <text x="725" y="90" text-anchor="middle" font-size="12">Subtree Weight = All blocks in subtree</text>
                        <text x="725" y="110" text-anchor="middle" font-size="12">Select heaviest subtree</text>
                        <text x="725" y="130" text-anchor="middle" font-size="12">Uncle blocks contribute to security</text>
                        <text x="725" y="150" text-anchor="middle" font-size="12">Faster block times possible</text>
                    </g>
                    
                    <!-- Arrow marker definition -->
                    <defs>
                        <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#7B1FA2"/>
                        </marker>
                    </defs>
                </svg>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">🔧</span>Ethereum's GHOST Implementation</h2>
            <div class="ghost-grid">
                <div class="ghost-card">
                    <h3>👨‍👩‍👧‍👦 Uncle Blocks</h3>
                    <p><strong>Orphaned blocks that contribute to security</strong></p>
                    <ul>
                        <li><strong>Definition:</strong> Valid blocks not in main chain</li>
                        <li><strong>Inclusion:</strong> Referenced by nephew blocks</li>
                        <li><strong>Reward:</strong> 87.5% of full block reward</li>
                        <li><strong>Limit:</strong> Maximum 2 uncles per block</li>
                        <li><strong>Depth:</strong> Up to 7 blocks deep</li>
                        <li><strong>Security:</strong> Increase overall chain weight</li>
                    </ul>
                </div>

                <div class="ghost-card">
                    <h3>⚖️ Weight Calculation</h3>
                    <p><strong>How GHOST determines the heaviest subtree</strong></p>
                    <ul>
                        <li><strong>Main Chain:</strong> Each block = 1 weight unit</li>
                        <li><strong>Uncle Blocks:</strong> Each uncle = 1 weight unit</li>
                        <li><strong>Subtree Weight:</strong> Sum of all blocks in subtree</li>
                        <li><strong>Selection Rule:</strong> Choose heaviest subtree</li>
                        <li><strong>Tie Breaking:</strong> Use block hash for determinism</li>
                        <li><strong>Finality:</strong> Probabilistic finality increases with depth</li>
                    </ul>
                </div>

                <div class="ghost-card">
                    <h3>⚡ Performance Benefits</h3>
                    <p><strong>Improvements over longest chain rule</strong></p>
                    <ul>
                        <li><strong>Faster Blocks:</strong> 12-second vs 10-minute blocks</li>
                        <li><strong>Higher Throughput:</strong> More transactions per second</li>
                        <li><strong>Reduced Waste:</strong> Orphaned work still contributes</li>
                        <li><strong>Better Incentives:</strong> Miners rewarded for honest work</li>
                        <li><strong>Network Efficiency:</strong> Less wasted computational power</li>
                        <li><strong>Faster Confirmation:</strong> Quicker transaction finality</li>
                    </ul>
                </div>

                <div class="ghost-card">
                    <h3>🛡️ Security Properties</h3>
                    <p><strong>How GHOST maintains blockchain security</strong></p>
                    <ul>
                        <li><strong>51% Threshold:</strong> Still requires majority hash power</li>
                        <li><strong>Uncle Contribution:</strong> Honest uncles strengthen main chain</li>
                        <li><strong>Attack Resistance:</strong> Harder to create competing chains</li>
                        <li><strong>Selfish Mining:</strong> Reduced effectiveness of selfish mining</li>
                        <li><strong>Network Splits:</strong> Better handling of temporary partitions</li>
                        <li><strong>Decentralization:</strong> Maintains mining decentralization</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">🔮</span>GHOST Evolution & Future</h2>
            <div class="concept-box">
                <h3>Beyond Ethereum's Implementation</h3>
                <p>GHOST has influenced many blockchain designs and continues to evolve:</p>
                
                <p><strong>Variations and Improvements:</strong></p>
                <ul>
                    <li><strong>SPECTRE:</strong> Generalized GHOST for DAG structures</li>
                    <li><strong>PHANTOM:</strong> Ordering transactions in DAG blocks</li>
                    <li><strong>Inclusive Protocols:</strong> Including more orphaned work</li>
                    <li><strong>Hybrid Approaches:</strong> Combining with other consensus mechanisms</li>
                </ul>
                
                <p><strong>Impact on Blockchain Design:</strong></p>
                <ul>
                    <li><strong>Block Time Optimization:</strong> Faster confirmation times</li>
                    <li><strong>Throughput Scaling:</strong> Higher transaction capacity</li>
                    <li><strong>Energy Efficiency:</strong> Less wasted computational work</li>
                    <li><strong>Fairness:</strong> Better rewards for honest miners</li>
                </ul>
                
                <p><strong>Key Takeaway:</strong> GHOST protocol represents a significant advancement in blockchain consensus, allowing for faster block times while maintaining security. By including orphaned blocks in weight calculations, it reduces waste and improves network efficiency. This innovation has influenced many subsequent blockchain designs and remains relevant for understanding modern consensus mechanisms.</p>
            </div>
        </div>

    </div>
</body>
</html>
