<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Question 5: Gas Limit in Ethereum</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh; line-height: 1.6; overflow-x: hidden;
        }
        .animated-bg {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: -1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            animation: gradientShift 20s ease infinite;
        }
        @keyframes gradientShift {
            0% { background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%); }
            25% { background: linear-gradient(135deg, #f093fb 0%, #667eea 50%, #764ba2 100%); }
            50% { background: linear-gradient(135deg, #764ba2 0%, #f093fb 50%, #667eea 100%); }
            75% { background: linear-gradient(135deg, #667eea 0%, #f093fb 50%, #764ba2 100%); }
            100% { background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%); }
        }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header {
            text-align: center; margin-bottom: 40px; padding: 40px 20px;
            background: rgba(255, 255, 255, 0.95); border-radius: 25px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1); backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .question-number {
            font-size: 4em; font-weight: 900; margin-bottom: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
            -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;
        }
        .main-title { font-size: 2.8em; font-weight: 700; color: #2c3e50; margin-bottom: 15px; }
        .subtitle { font-size: 1.3em; color: #666; margin-bottom: 25px; }
        .section {
            margin: 40px 0; padding: 35px; background: rgba(255, 255, 255, 0.95);
            border-radius: 20px; box-shadow: 0 15px 45px rgba(0,0,0,0.1);
            backdrop-filter: blur(15px); border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        .section:hover { transform: translateY(-5px); box-shadow: 0 25px 60px rgba(0,0,0,0.15); }
        .section h2 {
            font-size: 2.2em; color: #2c3e50; margin-bottom: 25px; font-weight: 700;
            display: flex; align-items: center; gap: 15px;
        }
        .emoji { font-size: 1.2em; filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1)); }
        .concept-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 15px; padding: 30px; margin: 25px 0;
            border-left: 6px solid #2196f3; box-shadow: 0 8px 25px rgba(33, 150, 243, 0.1);
        }
        .highlight {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            padding: 4px 10px; border-radius: 6px; font-weight: 600;
            color: #856404; display: inline-block;
        }
        .gas-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 25px; margin: 30px 0; }
        .gas-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
            border-radius: 15px; padding: 25px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease; border-top: 4px solid #FF9800;
        }
        .gas-card:hover { transform: translateY(-8px); box-shadow: 0 20px 50px rgba(0,0,0,0.15); }
        .gas-stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 25px 0; }
        .stat-card {
            background: linear-gradient(135deg, #FF9800, #F57C00); color: white; padding: 20px;
            border-radius: 12px; text-align: center; box-shadow: 0 8px 25px rgba(255, 152, 0, 0.3);
            transition: all 0.3s ease;
        }
        .stat-card:hover { transform: translateY(-5px); box-shadow: 0 12px 35px rgba(255, 152, 0, 0.4); }
        .stat-number { font-size: 2.5em; font-weight: 900; margin-bottom: 10px; }
        .stat-label { font-size: 1.1em; opacity: 0.9; }
        .diagram-container {
            background: rgba(248, 249, 250, 0.95); border: 2px solid rgba(222, 226, 230, 0.5);
            border-radius: 15px; padding: 25px; margin: 25px 0; overflow-x: auto; backdrop-filter: blur(10px);
        }
        @media (max-width: 768px) {
            .main-title { font-size: 2.2em; } .question-number { font-size: 3em; }
            .gas-grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="animated-bg"></div>
    <div class="container">
        <div class="header">
            <div class="question-number">05</div>
            <h1 class="main-title">⛽ Gas Limit in Ethereum</h1>
            <p class="subtitle">Understanding Transaction Costs & Network Performance</p>
        </div>

        <div class="section">
            <h2><span class="emoji">🔋</span>What is Gas in Ethereum?</h2>
            <div class="concept-box">
                <h3>Gas: The Fuel of Ethereum</h3>
                <p><span class="highlight">Gas</span> is a unit of measurement for computational work in Ethereum. Every operation (computation, storage, transaction) consumes a specific amount of gas. Users pay for gas in ETH to incentivize miners to process their transactions.</p>
                
                <p><strong>Key Gas Concepts:</strong></p>
                <ul>
                    <li><strong>Gas Limit:</strong> Maximum gas a transaction can consume</li>
                    <li><strong>Gas Price:</strong> Amount of ETH paid per unit of gas (in Gwei)</li>
                    <li><strong>Gas Used:</strong> Actual gas consumed by transaction execution</li>
                    <li><strong>Transaction Fee:</strong> Gas Used × Gas Price</li>
                    <li><strong>Block Gas Limit:</strong> Maximum gas all transactions in a block can consume</li>
                </ul>
            </div>

            <div class="gas-stats">
                <div class="stat-card">
                    <div class="stat-number">21,000</div>
                    <div class="stat-label">Basic Transfer Gas</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number">~50,000</div>
                    <div class="stat-label">ERC-20 Transfer Gas</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number">30M</div>
                    <div class="stat-label">Block Gas Limit</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number">20-100</div>
                    <div class="stat-label">Gas Price (Gwei)</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">⚙️</span>How Gas Limit Works</h2>
            <div class="diagram-container">
                <svg width="100%" height="400" viewBox="0 0 1200 400">
                    <text x="600" y="30" text-anchor="middle" font-size="18" font-weight="bold">GAS LIMIT & TRANSACTION EXECUTION</text>
                    
                    <!-- Transaction Creation -->
                    <rect x="100" y="60" width="150" height="80" fill="#E3F2FD" stroke="#2196F3" stroke-width="2" rx="10"/>
                    <text x="175" y="85" text-anchor="middle" font-size="12" font-weight="bold">TRANSACTION</text>
                    <text x="175" y="105" text-anchor="middle" font-size="10">Gas Limit: 100,000</text>
                    <text x="175" y="120" text-anchor="middle" font-size="10">Gas Price: 20 Gwei</text>
                    <text x="175" y="135" text-anchor="middle" font-size="10">Max Fee: 0.002 ETH</text>
                    
                    <!-- Execution Process -->
                    <rect x="350" y="60" width="150" height="80" fill="#FFF3E0" stroke="#FF9800" stroke-width="2" rx="10"/>
                    <text x="425" y="85" text-anchor="middle" font-size="12" font-weight="bold">EXECUTION</text>
                    <text x="425" y="105" text-anchor="middle" font-size="10">Operation 1: 5,000 gas</text>
                    <text x="425" y="120" text-anchor="middle" font-size="10">Operation 2: 10,000 gas</text>
                    <text x="425" y="135" text-anchor="middle" font-size="10">Total Used: 15,000 gas</text>
                    
                    <!-- Result -->
                    <rect x="600" y="60" width="150" height="80" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2" rx="10"/>
                    <text x="675" y="85" text-anchor="middle" font-size="12" font-weight="bold">RESULT</text>
                    <text x="675" y="105" text-anchor="middle" font-size="10">✓ Success</text>
                    <text x="675" y="120" text-anchor="middle" font-size="10">Fee: 0.0003 ETH</text>
                    <text x="675" y="135" text-anchor="middle" font-size="10">Refund: 0.0017 ETH</text>
                    
                    <!-- Failure Scenario -->
                    <rect x="850" y="60" width="150" height="80" fill="#FFEBEE" stroke="#F44336" stroke-width="2" rx="10"/>
                    <text x="925" y="85" text-anchor="middle" font-size="12" font-weight="bold">OUT OF GAS</text>
                    <text x="925" y="105" text-anchor="middle" font-size="10">✗ Failed</text>
                    <text x="925" y="120" text-anchor="middle" font-size="10">Fee: 0.002 ETH</text>
                    <text x="925" y="135" text-anchor="middle" font-size="10">No Refund</text>
                    
                    <!-- Arrows -->
                    <path d="M 250 100 L 350 100" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                    <path d="M 500 100 L 600 100" stroke="#4CAF50" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                    <path d="M 500 120 L 850 120" stroke="#F44336" stroke-width="2" fill="none" marker-end="url(#arrow)" stroke-dasharray="5,5"/>
                    
                    <!-- Gas Limit Examples -->
                    <text x="600" y="200" text-anchor="middle" font-size="16" font-weight="bold">COMMON GAS LIMITS</text>
                    
                    <!-- Simple Transfer -->
                    <rect x="100" y="230" width="200" height="50" fill="#BBDEFB" stroke="#2196F3" stroke-width="2" rx="5"/>
                    <text x="200" y="250" text-anchor="middle" font-size="12" font-weight="bold">ETH Transfer</text>
                    <text x="200" y="265" text-anchor="middle" font-size="11">Gas Limit: 21,000</text>
                    
                    <!-- Token Transfer -->
                    <rect x="350" y="230" width="200" height="50" fill="#C8E6C9" stroke="#4CAF50" stroke-width="2" rx="5"/>
                    <text x="450" y="250" text-anchor="middle" font-size="12" font-weight="bold">ERC-20 Transfer</text>
                    <text x="450" y="265" text-anchor="middle" font-size="11">Gas Limit: ~65,000</text>
                    
                    <!-- Smart Contract -->
                    <rect x="600" y="230" width="200" height="50" fill="#FFCDD2" stroke="#F44336" stroke-width="2" rx="5"/>
                    <text x="700" y="250" text-anchor="middle" font-size="12" font-weight="bold">Complex Contract</text>
                    <text x="700" y="265" text-anchor="middle" font-size="11">Gas Limit: 200,000+</text>
                    
                    <!-- Gas Price Impact -->
                    <text x="600" y="320" text-anchor="middle" font-size="14" font-weight="bold">Gas Price Impact on Transaction Speed</text>
                    
                    <text x="200" y="350" text-anchor="middle" font-size="11" fill="#F44336">Low Gas Price: Slow confirmation</text>
                    <text x="450" y="350" text-anchor="middle" font-size="11" fill="#FF9800">Medium Gas Price: Normal speed</text>
                    <text x="700" y="350" text-anchor="middle" font-size="11" fill="#4CAF50">High Gas Price: Fast confirmation</text>
                    
                    <!-- Arrow marker definition -->
                    <defs>
                        <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
                        </marker>
                    </defs>
                </svg>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">💡</span>Gas Optimization Strategies</h2>
            <div class="gas-grid">
                <div class="gas-card">
                    <h3>🎯 Smart Contract Optimization</h3>
                    <ul>
                        <li>Use efficient data types (uint256 vs smaller types)</li>
                        <li>Minimize storage operations (SSTORE costs 20,000 gas)</li>
                        <li>Batch operations to reduce transaction overhead</li>
                        <li>Use events instead of storage for logging</li>
                        <li>Optimize loops and conditional statements</li>
                    </ul>
                </div>

                <div class="gas-card">
                    <h3>⏰ Transaction Timing</h3>
                    <ul>
                        <li>Monitor network congestion for optimal timing</li>
                        <li>Use lower gas prices during off-peak hours</li>
                        <li>Set appropriate gas limits to avoid failures</li>
                        <li>Consider Layer 2 solutions for frequent transactions</li>
                        <li>Use gas price prediction tools</li>
                    </ul>
                </div>

                <div class="gas-card">
                    <h3>🔧 Technical Optimizations</h3>
                    <ul>
                        <li>Use CREATE2 for deterministic contract addresses</li>
                        <li>Implement proxy patterns for upgradeable contracts</li>
                        <li>Utilize assembly for gas-critical operations</li>
                        <li>Pack struct variables efficiently</li>
                        <li>Use libraries for common functionality</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">🌐</span>Impact on Network Performance</h2>
            <div class="concept-box">
                <h3>Gas Limit's Role in Network Scalability</h3>
                <p>Gas limits serve multiple critical functions in the Ethereum network:</p>
                
                <p><strong>Network Protection:</strong></p>
                <ul>
                    <li>Prevents infinite loops and DoS attacks</li>
                    <li>Limits computational load per block</li>
                    <li>Ensures network remains responsive</li>
                    <li>Provides economic incentives for efficiency</li>
                </ul>
                
                <p><strong>Economic Mechanism:</strong></p>
                <ul>
                    <li>Creates market for block space</li>
                    <li>Incentivizes miners through fees</li>
                    <li>Balances supply and demand for computation</li>
                    <li>Encourages efficient smart contract design</li>
                </ul>
                
                <p><strong>Key Takeaway:</strong> Gas limits are essential for Ethereum's security and performance, creating economic incentives for efficient code while protecting the network from abuse and ensuring fair resource allocation.</p>
            </div>
        </div>
    </div>
</body>
</html>
