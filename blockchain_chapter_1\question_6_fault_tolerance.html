<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Question 6: Fault Tolerance in Blockchain Systems</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            line-height: 1.6;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .section {
            margin: 30px 0;
            padding: 25px;
            border-radius: 12px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-left: 5px solid #667eea;
        }
        .diagram-container {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        .concept-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #2196f3;
        }
        .example-box {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #4caf50;
        }
        .advantages-box {
            background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 30%);
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #ff9800;
        }
        .disadvantages-box {
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #f44336;
        }
        .highlight {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            padding: 3px 8px;
            border-radius: 4px;
            font-weight: bold;
            color: #856404;
        }
        .code-snippet {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        th {
            background: #343a40;
            color: white;
            font-weight: bold;
        }
        tr:hover {
            background: #f8f9fa;
        }
        .emoji {
            font-size: 1.2em;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛡️ Question 6: Fault Tolerance & Its Significance in Blockchain</h1>

        <div class="section">
            <h2><span class="emoji">📚</span>What is Fault Tolerance?</h2>

            <div class="concept-box">
                <h3>Definition and Core Concepts</h3>
                <p><span class="highlight">Fault Tolerance</span> is the ability of a system to continue operating correctly even when some of its components fail. In blockchain systems, this means the network can maintain consensus and process transactions despite node failures, network partitions, or malicious attacks.</p>

                <p><strong>Types of Faults:</strong></p>
                <ul>
                    <li><strong>Crash Faults:</strong> Nodes stop working but don't send incorrect information</li>
                    <li><strong>Byzantine Faults:</strong> Nodes behave arbitrarily or maliciously</li>
                    <li><strong>Network Partitions:</strong> Communication failures between groups of nodes</li>
                    <li><strong>Timing Faults:</strong> Messages arrive too late or in wrong order</li>
                    <li><strong>Omission Faults:</strong> Messages are lost or not sent</li>
                </ul>
            </div>

            <div class="diagram-container">
                <svg width="100%" height="700" viewBox="0 0 1200 700">
                    <!-- Title -->
                    <text x="600" y="30" text-anchor="middle" font-size="18" font-weight="bold">FAULT TOLERANCE IN BLOCKCHAIN SYSTEMS</text>

                    <!-- Normal Operation vs Fault Scenarios -->
                    <text x="300" y="70" text-anchor="middle" font-size="16" font-weight="bold">NORMAL OPERATION</text>
                    <text x="900" y="70" text-anchor="middle" font-size="16" font-weight="bold">FAULT SCENARIOS</text>

                    <!-- Normal Operation -->
                    <g>
                        <!-- Healthy nodes -->
                        <circle cx="200" cy="120" r="25" fill="#4CAF50" stroke="#2E7D32" stroke-width="2"/>
                        <text x="200" y="125" text-anchor="middle" font-size="12" fill="white" font-weight="bold">N1</text>

                        <circle cx="300" cy="120" r="25" fill="#4CAF50" stroke="#2E7D32" stroke-width="2"/>
                        <text x="300" y="125" text-anchor="middle" font-size="12" fill="white" font-weight="bold">N2</text>

                        <circle cx="400" cy="120" r="25" fill="#4CAF50" stroke="#2E7D32" stroke-width="2"/>
                        <text x="400" y="125" text-anchor="middle" font-size="12" fill="white" font-weight="bold">N3</text>

                        <circle cx="250" cy="180" r="25" fill="#4CAF50" stroke="#2E7D32" stroke-width="2"/>
                        <text x="250" y="185" text-anchor="middle" font-size="12" fill="white" font-weight="bold">N4</text>

                        <circle cx="350" cy="180" r="25" fill="#4CAF50" stroke="#2E7D32" stroke-width="2"/>
                        <text x="350" y="185" text-anchor="middle" font-size="12" fill="white" font-weight="bold">N5</text>

                        <!-- Connections -->
                        <line x1="225" y1="120" x2="275" y2="120" stroke="#4CAF50" stroke-width="2"/>
                        <line x1="325" y1="120" x2="375" y2="120" stroke="#4CAF50" stroke-width="2"/>
                        <line x1="200" y1="145" x2="250" y2="155" stroke="#4CAF50" stroke-width="2"/>
                        <line x1="300" y1="145" x2="250" y2="155" stroke="#4CAF50" stroke-width="2"/>
                        <line x1="300" y1="145" x2="350" y2="155" stroke="#4CAF50" stroke-width="2"/>
                        <line x1="400" y1="145" x2="350" y2="155" stroke="#4CAF50" stroke-width="2"/>
                        <line x1="275" y1="180" x2="325" y2="180" stroke="#4CAF50" stroke-width="2"/>

                        <text x="300" y="220" text-anchor="middle" font-size="12" fill="#4CAF50" font-weight="bold">All nodes healthy</text>
                        <text x="300" y="235" text-anchor="middle" font-size="12" fill="#4CAF50" font-weight="bold">Consensus achieved</text>
                    </g>

                    <!-- Fault Scenarios -->
                    <g>
                        <!-- Mixed nodes (healthy, crashed, malicious) -->
                        <circle cx="800" cy="120" r="25" fill="#4CAF50" stroke="#2E7D32" stroke-width="2"/>
                        <text x="800" y="125" text-anchor="middle" font-size="12" fill="white" font-weight="bold">N1</text>

                        <circle cx="900" cy="120" r="25" fill="#F44336" stroke="#C62828" stroke-width="2"/>
                        <text x="900" y="125" text-anchor="middle" font-size="12" fill="white" font-weight="bold">N2</text>
                        <text x="900" y="105" text-anchor="middle" font-size="8" fill="#F44336">CRASHED</text>

                        <circle cx="1000" cy="120" r="25" fill="#4CAF50" stroke="#2E7D32" stroke-width="2"/>
                        <text x="1000" y="125" text-anchor="middle" font-size="12" fill="white" font-weight="bold">N3</text>

                        <circle cx="850" cy="180" r="25" fill="#FF9800" stroke="#F57C00" stroke-width="2"/>
                        <text x="850" y="185" text-anchor="middle" font-size="12" fill="white" font-weight="bold">N4</text>
                        <text x="850" y="205" text-anchor="middle" font-size="8" fill="#FF9800">MALICIOUS</text>

                        <circle cx="950" cy="180" r="25" fill="#4CAF50" stroke="#2E7D32" stroke-width="2"/>
                        <text x="950" y="185" text-anchor="middle" font-size="12" fill="white" font-weight="bold">N5</text>

                        <!-- Connections (some broken) -->
                        <line x1="825" y1="120" x2="875" y2="120" stroke="#F44336" stroke-width="2" stroke-dasharray="5,5"/>
                        <line x1="925" y1="120" x2="975" y2="120" stroke="#4CAF50" stroke-width="2"/>
                        <line x1="800" y1="145" x2="850" y2="155" stroke="#4CAF50" stroke-width="2"/>
                        <line x1="1000" y1="145" x2="950" y2="155" stroke="#4CAF50" stroke-width="2"/>
                        <line x1="875" y1="180" x2="925" y2="180" stroke="#FF9800" stroke-width="2" stroke-dasharray="10,5"/>

                        <text x="900" y="220" text-anchor="middle" font-size="12" fill="#F44336" font-weight="bold">Some nodes failed</text>
                        <text x="900" y="235" text-anchor="middle" font-size="12" fill="#4CAF50" font-weight="bold">System still operational</text>
                    </g>

                    <!-- Fault Tolerance Mechanisms -->
                    <text x="600" y="290" text-anchor="middle" font-size="16" font-weight="bold">FAULT TOLERANCE MECHANISMS</text>

                    <!-- Replication -->
                    <g>
                        <rect x="100" y="320" width="180" height="80" fill="#E3F2FD" stroke="#2196F3" stroke-width="2"/>
                        <text x="190" y="345" text-anchor="middle" font-size="14" font-weight="bold">REPLICATION</text>
                        <text x="190" y="365" text-anchor="middle" font-size="10">• Multiple copies of data</text>
                        <text x="190" y="380" text-anchor="middle" font-size="10">• Distributed across nodes</text>
                        <text x="190" y="395" text-anchor="middle" font-size="10">• Redundancy for reliability</text>
                    </g>

                    <!-- Consensus -->
                    <g>
                        <rect x="320" y="320" width="180" height="80" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2"/>
                        <text x="410" y="345" text-anchor="middle" font-size="14" font-weight="bold">CONSENSUS</text>
                        <text x="410" y="365" text-anchor="middle" font-size="10">• Agreement protocols</text>
                        <text x="410" y="380" text-anchor="middle" font-size="10">• Byzantine fault tolerance</text>
                        <text x="410" y="395" text-anchor="middle" font-size="10">• Majority voting</text>
                    </g>

                    <!-- Cryptographic Verification -->
                    <g>
                        <rect x="540" y="320" width="180" height="80" fill="#FFF3E0" stroke="#FF9800" stroke-width="2"/>
                        <text x="630" y="345" text-anchor="middle" font-size="14" font-weight="bold">CRYPTOGRAPHIC</text>
                        <text x="630" y="360" text-anchor="middle" font-size="14" font-weight="bold">VERIFICATION</text>
                        <text x="630" y="380" text-anchor="middle" font-size="10">• Digital signatures</text>
                        <text x="630" y="395" text-anchor="middle" font-size="10">• Hash verification</text>
                    </g>

                    <!-- Recovery Mechanisms -->
                    <g>
                        <rect x="760" y="320" width="180" height="80" fill="#F3E5F5" stroke="#9C27B0" stroke-width="2"/>
                        <text x="850" y="345" text-anchor="middle" font-size="14" font-weight="bold">RECOVERY</text>
                        <text x="850" y="360" text-anchor="middle" font-size="14" font-weight="bold">MECHANISMS</text>
                        <text x="850" y="380" text-anchor="middle" font-size="10">• Automatic healing</text>
                        <text x="850" y="395" text-anchor="middle" font-size="10">• Node rejoining</text>
                    </g>

                    <!-- Fault Tolerance Levels -->
                    <text x="600" y="450" text-anchor="middle" font-size="16" font-weight="bold">FAULT TOLERANCE LEVELS</text>

                    <!-- Crash Fault Tolerance -->
                    <g>
                        <rect x="150" y="480" width="200" height="60" fill="#BBDEFB" stroke="#2196F3" stroke-width="2"/>
                        <text x="250" y="505" text-anchor="middle" font-size="12" font-weight="bold">CRASH FAULT TOLERANCE</text>
                        <text x="250" y="520" text-anchor="middle" font-size="10">Tolerates: f failures</text>
                        <text x="250" y="535" text-anchor="middle" font-size="10">Requires: f + 1 nodes</text>
                    </g>

                    <!-- Byzantine Fault Tolerance -->
                    <g>
                        <rect x="400" y="480" width="200" height="60" fill="#C8E6C9" stroke="#4CAF50" stroke-width="2"/>
                        <text x="500" y="505" text-anchor="middle" font-size="12" font-weight="bold">BYZANTINE FAULT TOLERANCE</text>
                        <text x="500" y="520" text-anchor="middle" font-size="10">Tolerates: f failures</text>
                        <text x="500" y="535" text-anchor="middle" font-size="10">Requires: 3f + 1 nodes</text>
                    </g>

                    <!-- Practical Examples -->
                    <text x="600" y="580" text-anchor="middle" font-size="16" font-weight="bold">BLOCKCHAIN EXAMPLES</text>

                    <!-- Bitcoin -->
                    <g>
                        <rect x="100" y="600" width="150" height="50" fill="#FFE0B2" stroke="#FF9800" stroke-width="2"/>
                        <text x="175" y="620" text-anchor="middle" font-size="12" font-weight="bold">BITCOIN</text>
                        <text x="175" y="635" text-anchor="middle" font-size="10">51% attack threshold</text>
                        <text x="175" y="645" text-anchor="middle" font-size="8">Proof of Work</text>
                    </g>

                    <!-- Ethereum -->
                    <g>
                        <rect x="300" y="600" width="150" height="50" fill="#C8E6C9" stroke="#4CAF50" stroke-width="2"/>
                        <text x="375" y="620" text-anchor="middle" font-size="12" font-weight="bold">ETHEREUM 2.0</text>
                        <text x="375" y="635" text-anchor="middle" font-size="10">33% attack threshold</text>
                        <text x="375" y="645" text-anchor="middle" font-size="8">Proof of Stake</text>
                    </g>

                    <!-- Hyperledger -->
                    <g>
                        <rect x="500" y="600" width="150" height="50" fill="#E1BEE7" stroke="#9C27B0" stroke-width="2"/>
                        <text x="575" y="620" text-anchor="middle" font-size="12" font-weight="bold">HYPERLEDGER</text>
                        <text x="575" y="635" text-anchor="middle" font-size="10">33% fault tolerance</text>
                        <text x="575" y="645" text-anchor="middle" font-size="8">PBFT variants</text>
                    </g>

                    <!-- Tendermint -->
                    <g>
                        <rect x="700" y="600" width="150" height="50" fill="#FFCDD2" stroke="#F44336" stroke-width="2"/>
                        <text x="775" y="620" text-anchor="middle" font-size="12" font-weight="bold">TENDERMINT</text>
                        <text x="775" y="635" text-anchor="middle" font-size="10">33% fault tolerance</text>
                        <text x="775" y="645" text-anchor="middle" font-size="8">BFT Consensus</text>
                    </g>
                </svg>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">⚙️</span>How Blockchain Achieves Fault Tolerance</h2>

            <div class="example-box">
                <h3>Fault Tolerance Mechanisms</h3>
                <div class="code-snippet">
1. REPLICATION:
   - Multiple copies of blockchain across nodes
   - Each node maintains complete ledger
   - Redundancy ensures data availability

2. CONSENSUS MECHANISMS:
   - Proof of Work: Longest chain rule
   - Proof of Stake: Validator selection
   - PBFT: Three-phase voting protocol

3. CRYPTOGRAPHIC VERIFICATION:
   - Digital signatures prevent tampering
   - Hash chains ensure integrity
   - Merkle trees enable efficient verification

4. RECOVERY MECHANISMS:
   - Nodes can rejoin network
   - Automatic synchronization
   - Self-healing properties

5. INCENTIVE ALIGNMENT:
   - Economic rewards for honest behavior
   - Penalties for malicious actions
   - Game theory ensures cooperation
                </div>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">✅</span>Benefits of Fault Tolerance</h2>

            <div class="advantages-box">
                <h3>Key Advantages</h3>
                <ul>
                    <li><strong>High Availability:</strong> System remains operational 24/7</li>
                    <li><strong>Data Integrity:</strong> Information remains accurate despite failures</li>
                    <li><strong>Trust:</strong> Users can rely on system without central authority</li>
                    <li><strong>Resilience:</strong> Withstands various types of attacks and failures</li>
                    <li><strong>Scalability:</strong> Can grow without compromising reliability</li>
                    <li><strong>Decentralization:</strong> No single point of failure</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">❌</span>Challenges and Trade-offs</h2>

            <div class="disadvantages-box">
                <h3>Limitations</h3>
                <ul>
                    <li><strong>Performance Overhead:</strong> Consensus mechanisms slow down transactions</li>
                    <li><strong>Resource Consumption:</strong> Replication requires more storage and bandwidth</li>
                    <li><strong>Complexity:</strong> Difficult to design and implement correctly</li>
                    <li><strong>Cost:</strong> More expensive than centralized systems</li>
                    <li><strong>Scalability Limits:</strong> Performance decreases with more participants</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">🚀</span>Applications and Significance</h2>

            <div class="example-box">
                <h3>Critical Applications</h3>
                <ul>
                    <li><strong>Financial Systems:</strong> Critical for handling monetary transactions</li>
                    <li><strong>Supply Chain:</strong> Ensures data integrity across multiple parties</li>
                    <li><strong>Healthcare:</strong> Protects sensitive medical records</li>
                    <li><strong>Voting Systems:</strong> Maintains election integrity</li>
                    <li><strong>Smart Contracts:</strong> Reliable execution of automated agreements</li>
                </ul>
            </div>
        </div>

    </div>
</body>
</html>
