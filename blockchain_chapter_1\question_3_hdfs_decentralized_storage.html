<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Question 3: HDFS & Decentralized Storage</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            line-height: 1.6;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .section {
            margin: 30px 0;
            padding: 25px;
            border-radius: 12px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-left: 5px solid #667eea;
        }
        .diagram-container {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        .concept-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #2196f3;
        }
        .example-box {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #4caf50;
        }
        .advantages-box {
            background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 30%);
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #ff9800;
        }
        .disadvantages-box {
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #f44336;
        }
        .highlight {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            padding: 3px 8px;
            border-radius: 4px;
            font-weight: bold;
            color: #856404;
        }
        .code-snippet {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        th {
            background: #343a40;
            color: white;
            font-weight: bold;
        }
        tr:hover {
            background: #f8f9fa;
        }
        .emoji {
            font-size: 1.2em;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📁 Question 3: Hadoop Distributed File System (HDFS) & Decentralized Storage</h1>

        <div class="section">
            <h2><span class="emoji">📚</span>What is HDFS?</h2>

            <div class="concept-box">
                <h3>Definition and Core Architecture</h3>
                <p>The <span class="highlight">Hadoop Distributed File System (HDFS)</span> is a distributed file system designed to store very large files across multiple machines in a reliable, fault-tolerant manner. It's the storage foundation of the Apache Hadoop ecosystem, optimized for high-throughput access to application data.</p>

                <p><strong>Key Design Principles:</strong></p>
                <ul>
                    <li><strong>Hardware Failure is Normal:</strong> Designed to detect and recover from failures automatically</li>
                    <li><strong>Streaming Data Access:</strong> Optimized for batch processing rather than interactive use</li>
                    <li><strong>Large Data Sets:</strong> Designed to store files ranging from gigabytes to terabytes</li>
                    <li><strong>Simple Coherency Model:</strong> Write-once-read-many access model</li>
                    <li><strong>Moving Computation is Cheaper:</strong> Computation moves to data, not vice versa</li>
                </ul>
            </div>

            <div class="diagram-container">
                <svg width="100%" height="700" viewBox="0 0 1200 700">
                    <!-- Title -->
                    <text x="600" y="30" text-anchor="middle" font-size="18" font-weight="bold">HDFS ARCHITECTURE OVERVIEW</text>

                    <!-- NameNode (Master) -->
                    <rect x="500" y="60" width="200" height="100" fill="#FFE0B2" stroke="#FF9800" stroke-width="3"/>
                    <text x="600" y="90" text-anchor="middle" font-size="16" font-weight="bold">NAMENODE</text>
                    <text x="600" y="110" text-anchor="middle" font-size="12">(Master Server)</text>
                    <text x="600" y="125" text-anchor="middle" font-size="10">• Manages file system metadata</text>
                    <text x="600" y="140" text-anchor="middle" font-size="10">• Maintains file system tree</text>
                    <text x="600" y="155" text-anchor="middle" font-size="10">• Tracks block locations</text>

                    <!-- Secondary NameNode -->
                    <rect x="750" y="60" width="150" height="100" fill="#FFCDD2" stroke="#F44336" stroke-width="2"/>
                    <text x="825" y="90" text-anchor="middle" font-size="14" font-weight="bold">SECONDARY</text>
                    <text x="825" y="105" text-anchor="middle" font-size="14" font-weight="bold">NAMENODE</text>
                    <text x="825" y="125" text-anchor="middle" font-size="10">• Checkpoint creation</text>
                    <text x="825" y="140" text-anchor="middle" font-size="10">• Metadata backup</text>
                    <text x="825" y="155" text-anchor="middle" font-size="10">• Not a failover node</text>

                    <!-- DataNodes (Workers) -->
                    <text x="600" y="200" text-anchor="middle" font-size="16" font-weight="bold">DATANODES (Worker Nodes)</text>

                    <!-- DataNode 1 -->
                    <rect x="100" y="230" width="150" height="120" fill="#C8E6C9" stroke="#4CAF50" stroke-width="2"/>
                    <text x="175" y="255" text-anchor="middle" font-size="14" font-weight="bold">DATANODE 1</text>
                    <text x="175" y="275" text-anchor="middle" font-size="10">• Stores data blocks</text>
                    <text x="175" y="290" text-anchor="middle" font-size="10">• Serves read/write requests</text>
                    <text x="175" y="305" text-anchor="middle" font-size="10">• Sends heartbeats</text>

                    <!-- Data blocks in DataNode 1 -->
                    <rect x="110" y="315" width="30" height="15" fill="#4CAF50"/>
                    <text x="125" y="325" text-anchor="middle" font-size="8" fill="white">B1</text>
                    <rect x="145" y="315" width="30" height="15" fill="#4CAF50"/>
                    <text x="160" y="325" text-anchor="middle" font-size="8" fill="white">B2</text>
                    <rect x="180" y="315" width="30" height="15" fill="#4CAF50"/>
                    <text x="195" y="325" text-anchor="middle" font-size="8" fill="white">B5</text>
                    <rect x="215" y="315" width="30" height="15" fill="#4CAF50"/>
                    <text x="230" y="325" text-anchor="middle" font-size="8" fill="white">B7</text>

                    <!-- DataNode 2 -->
                    <rect x="300" y="230" width="150" height="120" fill="#C8E6C9" stroke="#4CAF50" stroke-width="2"/>
                    <text x="375" y="255" text-anchor="middle" font-size="14" font-weight="bold">DATANODE 2</text>
                    <text x="375" y="275" text-anchor="middle" font-size="10">• Stores data blocks</text>
                    <text x="375" y="290" text-anchor="middle" font-size="10">• Serves read/write requests</text>
                    <text x="375" y="305" text-anchor="middle" font-size="10">• Sends heartbeats</text>

                    <!-- Data blocks in DataNode 2 -->
                    <rect x="310" y="315" width="30" height="15" fill="#4CAF50"/>
                    <text x="325" y="325" text-anchor="middle" font-size="8" fill="white">B1</text>
                    <rect x="345" y="315" width="30" height="15" fill="#4CAF50"/>
                    <text x="360" y="325" text-anchor="middle" font-size="8" fill="white">B3</text>
                    <rect x="380" y="315" width="30" height="15" fill="#4CAF50"/>
                    <text x="395" y="325" text-anchor="middle" font-size="8" fill="white">B4</text>
                    <rect x="415" y="315" width="30" height="15" fill="#4CAF50"/>
                    <text x="430" y="325" text-anchor="middle" font-size="8" fill="white">B6</text>

                    <!-- DataNode 3 -->
                    <rect x="500" y="230" width="150" height="120" fill="#C8E6C9" stroke="#4CAF50" stroke-width="2"/>
                    <text x="575" y="255" text-anchor="middle" font-size="14" font-weight="bold">DATANODE 3</text>
                    <text x="575" y="275" text-anchor="middle" font-size="10">• Stores data blocks</text>
                    <text x="575" y="290" text-anchor="middle" font-size="10">• Serves read/write requests</text>
                    <text x="575" y="305" text-anchor="middle" font-size="10">• Sends heartbeats</text>

                    <!-- Data blocks in DataNode 3 -->
                    <rect x="510" y="315" width="30" height="15" fill="#4CAF50"/>
                    <text x="525" y="325" text-anchor="middle" font-size="8" fill="white">B2</text>
                    <rect x="545" y="315" width="30" height="15" fill="#4CAF50"/>
                    <text x="560" y="325" text-anchor="middle" font-size="8" fill="white">B3</text>
                    <rect x="580" y="315" width="30" height="15" fill="#4CAF50"/>
                    <text x="595" y="325" text-anchor="middle" font-size="8" fill="white">B4</text>
                    <rect x="615" y="315" width="30" height="15" fill="#4CAF50"/>
                    <text x="630" y="325" text-anchor="middle" font-size="8" fill="white">B8</text>

                    <!-- DataNode 4 -->
                    <rect x="700" y="230" width="150" height="120" fill="#C8E6C9" stroke="#4CAF50" stroke-width="2"/>
                    <text x="775" y="255" text-anchor="middle" font-size="14" font-weight="bold">DATANODE 4</text>
                    <text x="775" y="275" text-anchor="middle" font-size="10">• Stores data blocks</text>
                    <text x="775" y="290" text-anchor="middle" font-size="10">• Serves read/write requests</text>
                    <text x="775" y="305" text-anchor="middle" font-size="10">• Sends heartbeats</text>

                    <!-- Data blocks in DataNode 4 -->
                    <rect x="710" y="315" width="30" height="15" fill="#4CAF50"/>
                    <text x="725" y="325" text-anchor="middle" font-size="8" fill="white">B5</text>
                    <rect x="745" y="315" width="30" height="15" fill="#4CAF50"/>
                    <text x="760" y="325" text-anchor="middle" font-size="8" fill="white">B6</text>
                    <rect x="780" y="315" width="30" height="15" fill="#4CAF50"/>
                    <text x="795" y="325" text-anchor="middle" font-size="8" fill="white">B7</text>
                    <rect x="815" y="315" width="30" height="15" fill="#4CAF50"/>
                    <text x="830" y="325" text-anchor="middle" font-size="8" fill="white">B8</text>

                    <!-- Client -->
                    <rect x="950" y="230" width="120" height="80" fill="#E3F2FD" stroke="#2196F3" stroke-width="2"/>
                    <text x="1010" y="260" text-anchor="middle" font-size="14" font-weight="bold">CLIENT</text>
                    <text x="1010" y="280" text-anchor="middle" font-size="10">• Reads/Writes files</text>
                    <text x="1010" y="295" text-anchor="middle" font-size="10">• Contacts NameNode</text>

                    <!-- Communication lines -->
                    <!-- NameNode to DataNodes (metadata) -->
                    <line x1="550" y1="160" x2="175" y2="230" stroke="#FF9800" stroke-width="2" stroke-dasharray="5,5"/>
                    <line x1="580" y1="160" x2="375" y2="230" stroke="#FF9800" stroke-width="2" stroke-dasharray="5,5"/>
                    <line x1="620" y1="160" x2="575" y2="230" stroke="#FF9800" stroke-width="2" stroke-dasharray="5,5"/>
                    <line x1="650" y1="160" x2="775" y2="230" stroke="#FF9800" stroke-width="2" stroke-dasharray="5,5"/>

                    <!-- Client to NameNode -->
                    <line x1="950" y1="250" x2="700" y2="120" stroke="#2196F3" stroke-width="3"/>
                    <text x="825" y="180" text-anchor="middle" font-size="10" fill="#2196F3">1. Metadata Request</text>

                    <!-- Client to DataNodes -->
                    <line x1="950" y1="280" x2="850" y2="290" stroke="#4CAF50" stroke-width="3"/>
                    <text x="900" y="300" text-anchor="middle" font-size="10" fill="#4CAF50">2. Data Transfer</text>

                    <!-- File Structure Example -->
                    <text x="600" y="400" text-anchor="middle" font-size="16" font-weight="bold">FILE STORAGE EXAMPLE</text>

                    <!-- Large file representation -->
                    <rect x="200" y="420" width="800" height="40" fill="#E1BEE7" stroke="#9C27B0" stroke-width="2"/>
                    <text x="600" y="445" text-anchor="middle" font-size="14" font-weight="bold">LARGE FILE (1GB)</text>

                    <!-- Block division -->
                    <text x="600" y="480" text-anchor="middle" font-size="12">Divided into blocks (128MB each)</text>

                    <!-- Individual blocks -->
                    <rect x="200" y="500" width="100" height="30" fill="#FFCDD2" stroke="#F44336" stroke-width="1"/>
                    <text x="250" y="520" text-anchor="middle" font-size="10">Block 1 (128MB)</text>

                    <rect x="320" y="500" width="100" height="30" fill="#FFCDD2" stroke="#F44336" stroke-width="1"/>
                    <text x="370" y="520" text-anchor="middle" font-size="10">Block 2 (128MB)</text>

                    <rect x="440" y="500" width="100" height="30" fill="#FFCDD2" stroke="#F44336" stroke-width="1"/>
                    <text x="490" y="520" text-anchor="middle" font-size="10">Block 3 (128MB)</text>

                    <rect x="560" y="500" width="100" height="30" fill="#FFCDD2" stroke="#F44336" stroke-width="1"/>
                    <text x="610" y="520" text-anchor="middle" font-size="10">Block 4 (128MB)</text>

                    <rect x="680" y="500" width="100" height="30" fill="#FFCDD2" stroke="#F44336" stroke-width="1"/>
                    <text x="730" y="520" text-anchor="middle" font-size="10">Block 5 (128MB)</text>

                    <rect x="800" y="500" width="100" height="30" fill="#FFCDD2" stroke="#F44336" stroke-width="1"/>
                    <text x="850" y="520" text-anchor="middle" font-size="10">Block 6 (128MB)</text>

                    <rect x="920" y="500" width="80" height="30" fill="#FFCDD2" stroke="#F44336" stroke-width="1"/>
                    <text x="960" y="520" text-anchor="middle" font-size="10">Block 7 (96MB)</text>

                    <!-- Replication illustration -->
                    <text x="600" y="560" text-anchor="middle" font-size="12">Each block replicated 3 times across different DataNodes</text>

                    <!-- Replication example for Block 1 -->
                    <text x="250" y="580" text-anchor="middle" font-size="10" font-weight="bold">Block 1 Replicas:</text>
                    <circle cx="200" cy="600" r="15" fill="#4CAF50"/>
                    <text x="200" y="605" text-anchor="middle" font-size="8" fill="white">DN1</text>
                    <circle cx="250" cy="600" r="15" fill="#4CAF50"/>
                    <text x="250" y="605" text-anchor="middle" font-size="8" fill="white">DN2</text>
                    <circle cx="300" cy="600" r="15" fill="#4CAF50"/>
                    <text x="300" y="605" text-anchor="middle" font-size="8" fill="white">DN4</text>

                    <!-- Fault tolerance note -->
                    <text x="600" y="640" text-anchor="middle" font-size="12" fill="#F44336" font-weight="bold">Fault Tolerance: Can lose up to 2 DataNodes without data loss</text>

                    <!-- Key Features -->
                    <text x="100" y="680" font-size="12" font-weight="bold">Key Features:</text>
                    <text x="100" y="695" font-size="10">• Default block size: 128MB (configurable)</text>
                    <text x="350" y="695" font-size="10">• Default replication factor: 3</text>
                    <text x="600" y="695" font-size="10">• Rack-aware placement</text>
                    <text x="800" y="695" font-size="10">• Automatic failure detection</text>
                </svg>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">⚙️</span>How HDFS Works</h2>

            <div class="example-box">
                <h3>HDFS Operation Process</h3>
                <div class="code-snippet">
1. FILE WRITE PROCESS:
   Client → NameNode: "I want to write file.txt (1GB)"
   NameNode → Client: "Split into 8 blocks, write to these DataNodes..."
   Client → DataNodes: Writes blocks with replication pipeline
   DataNodes → NameNode: "Block written successfully"

2. FILE READ PROCESS:
   Client → NameNode: "Where is file.txt?"
   NameNode → Client: "Blocks are at these locations..."
   Client → DataNodes: Reads blocks directly (parallel)
   Client: Reconstructs original file from blocks
                </div>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">✅</span>Advantages of HDFS</h2>

            <div class="advantages-box">
                <h3>Key Benefits</h3>
                <ul>
                    <li><strong>Fault Tolerance:</strong> Automatic recovery from hardware failures</li>
                    <li><strong>Scalability:</strong> Can scale to thousands of nodes</li>
                    <li><strong>High Throughput:</strong> Optimized for large sequential operations</li>
                    <li><strong>Cost-Effective:</strong> Runs on commodity hardware</li>
                    <li><strong>Data Locality:</strong> Computation moves to data</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">❌</span>Disadvantages and Limitations</h2>

            <div class="disadvantages-box">
                <h3>Key Limitations</h3>
                <ul>
                    <li><strong>Single Point of Failure:</strong> NameNode dependency</li>
                    <li><strong>Small File Problem:</strong> Inefficient for many small files</li>
                    <li><strong>Write-Once Model:</strong> No file modifications</li>
                    <li><strong>High Latency:</strong> Not suitable for real-time apps</li>
                    <li><strong>Memory Limitations:</strong> NameNode memory constraints</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">🚀</span>Applications in Decentralized Storage</h2>

            <div class="example-box">
                <h3>Use Cases</h3>
                <ul>
                    <li><strong>Big Data Analytics:</strong> Store massive datasets for analysis</li>
                    <li><strong>Data Warehousing:</strong> Foundation for distributed warehouses</li>
                    <li><strong>Content Distribution:</strong> Store and distribute large media files</li>
                    <li><strong>Machine Learning:</strong> Store training datasets</li>
                    <li><strong>Blockchain Integration:</strong> Off-chain storage solutions</li>
                </ul>
            </div>
        </div>

    </div>
</body>
</html>
