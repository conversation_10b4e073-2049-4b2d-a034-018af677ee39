<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Question 8: Digital Signatures & ECDSA in Blockchain Transactions</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            line-height: 1.6;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .section {
            margin: 30px 0;
            padding: 25px;
            border-radius: 12px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-left: 5px solid #667eea;
        }
        .diagram-container {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        .concept-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #2196f3;
        }
        .example-box {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #4caf50;
        }
        .advantages-box {
            background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 30%);
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #ff9800;
        }
        .disadvantages-box {
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #f44336;
        }
        .highlight {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            padding: 3px 8px;
            border-radius: 4px;
            font-weight: bold;
            color: #856404;
        }
        .code-snippet {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        th {
            background: #343a40;
            color: white;
            font-weight: bold;
        }
        tr:hover {
            background: #f8f9fa;
        }
        .emoji {
            font-size: 1.2em;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>✍️ Question 8: Digital Signatures & ECDSA in Blockchain Transactions</h1>

        <div class="section">
            <h2><span class="emoji">📚</span>What are Digital Signatures?</h2>

            <div class="concept-box">
                <h3>Definition and Core Concepts</h3>
                <p><span class="highlight">Digital signatures</span> are cryptographic mechanisms that provide authentication, integrity, and non-repudiation for digital messages. They use public-key cryptography to prove that a message was created by the holder of a private key without revealing the private key itself.</p>

                <p><strong>ECDSA (Elliptic Curve Digital Signature Algorithm):</strong></p>
                <ul>
                    <li><strong>Elliptic Curve Cryptography:</strong> Based on mathematical properties of elliptic curves</li>
                    <li><strong>Smaller Key Sizes:</strong> 256-bit ECDSA ≈ 3072-bit RSA security</li>
                    <li><strong>Efficiency:</strong> Faster computation and smaller signatures</li>
                    <li><strong>Blockchain Standard:</strong> Used by Bitcoin, Ethereum, and most cryptocurrencies</li>
                    <li><strong>Security:</strong> Based on discrete logarithm problem on elliptic curves</li>
                </ul>
            </div>

            <div class="diagram-container">
                <svg width="100%" height="700" viewBox="0 0 1200 700">
                    <!-- Title -->
                    <text x="600" y="30" text-anchor="middle" font-size="18" font-weight="bold">DIGITAL SIGNATURE PROCESS WITH ECDSA</text>

                    <!-- Key Generation -->
                    <text x="600" y="70" text-anchor="middle" font-size="16" font-weight="bold">1. KEY GENERATION</text>

                    <g>
                        <!-- Private Key -->
                        <rect x="200" y="90" width="150" height="60" fill="#FFCDD2" stroke="#F44336" stroke-width="2"/>
                        <text x="275" y="115" text-anchor="middle" font-size="12" font-weight="bold">PRIVATE KEY</text>
                        <text x="275" y="130" text-anchor="middle" font-size="10">(Random 256-bit number)</text>
                        <text x="275" y="145" text-anchor="middle" font-size="10">Keep Secret!</text>

                        <!-- Arrow -->
                        <path d="M 350 120 L 450 120" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                        <text x="400" y="110" text-anchor="middle" font-size="10">Elliptic Curve</text>
                        <text x="400" y="135" text-anchor="middle" font-size="10">Multiplication</text>

                        <!-- Public Key -->
                        <rect x="500" y="90" width="150" height="60" fill="#C8E6C9" stroke="#4CAF50" stroke-width="2"/>
                        <text x="575" y="115" text-anchor="middle" font-size="12" font-weight="bold">PUBLIC KEY</text>
                        <text x="575" y="130" text-anchor="middle" font-size="10">(Point on curve)</text>
                        <text x="575" y="145" text-anchor="middle" font-size="10">Share Publicly</text>
                    </g>

                    <!-- Signature Creation -->
                    <text x="600" y="200" text-anchor="middle" font-size="16" font-weight="bold">2. SIGNATURE CREATION</text>

                    <g>
                        <!-- Message -->
                        <rect x="100" y="220" width="150" height="50" fill="#E3F2FD" stroke="#2196F3" stroke-width="2"/>
                        <text x="175" y="240" text-anchor="middle" font-size="12" font-weight="bold">MESSAGE</text>
                        <text x="175" y="255" text-anchor="middle" font-size="10">(Transaction data)</text>

                        <!-- Hash -->
                        <rect x="300" y="220" width="150" height="50" fill="#FFF3E0" stroke="#FF9800" stroke-width="2"/>
                        <text x="375" y="240" text-anchor="middle" font-size="12" font-weight="bold">HASH</text>
                        <text x="375" y="255" text-anchor="middle" font-size="10">(SHA-256)</text>

                        <!-- Private Key -->
                        <rect x="500" y="220" width="150" height="50" fill="#FFCDD2" stroke="#F44336" stroke-width="2"/>
                        <text x="575" y="240" text-anchor="middle" font-size="12" font-weight="bold">PRIVATE KEY</text>
                        <text x="575" y="255" text-anchor="middle" font-size="10">(For signing)</text>

                        <!-- ECDSA Algorithm -->
                        <rect x="700" y="220" width="150" height="50" fill="#E1BEE7" stroke="#9C27B0" stroke-width="2"/>
                        <text x="775" y="240" text-anchor="middle" font-size="12" font-weight="bold">ECDSA</text>
                        <text x="775" y="255" text-anchor="middle" font-size="10">(Signature Algorithm)</text>

                        <!-- Signature -->
                        <rect x="900" y="220" width="150" height="50" fill="#DCEDC8" stroke="#689F38" stroke-width="2"/>
                        <text x="975" y="240" text-anchor="middle" font-size="12" font-weight="bold">SIGNATURE</text>
                        <text x="975" y="255" text-anchor="middle" font-size="10">(r, s) values</text>

                        <!-- Arrows -->
                        <path d="M 250 245 L 300 245" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                        <path d="M 450 245 L 500 245" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                        <path d="M 650 245 L 700 245" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                        <path d="M 850 245 L 900 245" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>

                        <!-- Combine arrow -->
                        <path d="M 375 270 L 775 270 L 775 220" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)" stroke-dasharray="5,5"/>
                        <path d="M 575 270 L 775 270" stroke="#333" stroke-width="2" fill="none" stroke-dasharray="5,5"/>
                    </g>

                    <!-- Signature Verification -->
                    <text x="600" y="330" text-anchor="middle" font-size="16" font-weight="bold">3. SIGNATURE VERIFICATION</text>

                    <g>
                        <!-- Message -->
                        <rect x="100" y="350" width="120" height="50" fill="#E3F2FD" stroke="#2196F3" stroke-width="2"/>
                        <text x="160" y="370" text-anchor="middle" font-size="12" font-weight="bold">MESSAGE</text>
                        <text x="160" y="385" text-anchor="middle" font-size="10">(Same data)</text>

                        <!-- Signature -->
                        <rect x="250" y="350" width="120" height="50" fill="#DCEDC8" stroke="#689F38" stroke-width="2"/>
                        <text x="310" y="370" text-anchor="middle" font-size="12" font-weight="bold">SIGNATURE</text>
                        <text x="310" y="385" text-anchor="middle" font-size="10">(r, s) values</text>

                        <!-- Public Key -->
                        <rect x="400" y="350" width="120" height="50" fill="#C8E6C9" stroke="#4CAF50" stroke-width="2"/>
                        <text x="460" y="370" text-anchor="middle" font-size="12" font-weight="bold">PUBLIC KEY</text>
                        <text x="460" y="385" text-anchor="middle" font-size="10">(Known to all)</text>

                        <!-- Verification Algorithm -->
                        <rect x="550" y="350" width="150" height="50" fill="#E1BEE7" stroke="#9C27B0" stroke-width="2"/>
                        <text x="625" y="370" text-anchor="middle" font-size="12" font-weight="bold">VERIFICATION</text>
                        <text x="625" y="385" text-anchor="middle" font-size="10">(ECDSA Verify)</text>

                        <!-- Result -->
                        <rect x="750" y="350" width="120" height="50" fill="#C8E6C9" stroke="#4CAF50" stroke-width="2"/>
                        <text x="810" y="370" text-anchor="middle" font-size="12" font-weight="bold">VALID</text>
                        <text x="810" y="385" text-anchor="middle" font-size="10">✓ Authentic</text>

                        <!-- Arrows -->
                        <path d="M 220 375 L 250 375" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                        <path d="M 370 375 L 400 375" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                        <path d="M 520 375 L 550 375" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                        <path d="M 700 375 L 750 375" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>

                        <!-- Combine arrows -->
                        <path d="M 160 400 L 625 400 L 625 350" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)" stroke-dasharray="5,5"/>
                        <path d="M 310 400 L 625 400" stroke="#333" stroke-width="2" fill="none" stroke-dasharray="5,5"/>
                        <path d="M 460 400 L 625 400" stroke="#333" stroke-width="2" fill="none" stroke-dasharray="5,5"/>
                    </g>

                    <!-- Blockchain Transaction Example -->
                    <text x="600" y="460" text-anchor="middle" font-size="16" font-weight="bold">BLOCKCHAIN TRANSACTION EXAMPLE</text>

                    <g>
                        <!-- Transaction Data -->
                        <rect x="100" y="480" width="200" height="80" fill="#E3F2FD" stroke="#2196F3" stroke-width="2"/>
                        <text x="200" y="505" text-anchor="middle" font-size="12" font-weight="bold">TRANSACTION DATA</text>
                        <text x="200" y="520" text-anchor="middle" font-size="10">From: Alice</text>
                        <text x="200" y="535" text-anchor="middle" font-size="10">To: Bob</text>
                        <text x="200" y="550" text-anchor="middle" font-size="10">Amount: 1.5 BTC</text>

                        <!-- Alice's Signature -->
                        <rect x="350" y="480" width="200" height="80" fill="#FFCDD2" stroke="#F44336" stroke-width="2"/>
                        <text x="450" y="505" text-anchor="middle" font-size="12" font-weight="bold">ALICE'S SIGNATURE</text>
                        <text x="450" y="520" text-anchor="middle" font-size="10">r: 0x1a2b3c...</text>
                        <text x="450" y="535" text-anchor="middle" font-size="10">s: 0x4d5e6f...</text>
                        <text x="450" y="550" text-anchor="middle" font-size="10">Proves Alice authorized</text>

                        <!-- Network Verification -->
                        <rect x="600" y="480" width="200" height="80" fill="#C8E6C9" stroke="#4CAF50" stroke-width="2"/>
                        <text x="700" y="505" text-anchor="middle" font-size="12" font-weight="bold">NETWORK VERIFICATION</text>
                        <text x="700" y="520" text-anchor="middle" font-size="10">Uses Alice's public key</text>
                        <text x="700" y="535" text-anchor="middle" font-size="10">Verifies signature</text>
                        <text x="700" y="550" text-anchor="middle" font-size="10">Accepts transaction</text>

                        <!-- Arrows -->
                        <path d="M 300 520 L 350 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                        <path d="M 550 520 L 600 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                    </g>

                    <!-- Security Properties -->
                    <text x="600" y="600" text-anchor="middle" font-size="16" font-weight="bold">SECURITY PROPERTIES</text>

                    <g>
                        <!-- Authentication -->
                        <rect x="100" y="620" width="150" height="50" fill="#BBDEFB" stroke="#2196F3" stroke-width="2"/>
                        <text x="175" y="640" text-anchor="middle" font-size="12" font-weight="bold">AUTHENTICATION</text>
                        <text x="175" y="655" text-anchor="middle" font-size="10">Proves sender identity</text>

                        <!-- Integrity -->
                        <rect x="300" y="620" width="150" height="50" fill="#C8E6C9" stroke="#4CAF50" stroke-width="2"/>
                        <text x="375" y="640" text-anchor="middle" font-size="12" font-weight="bold">INTEGRITY</text>
                        <text x="375" y="655" text-anchor="middle" font-size="10">Detects tampering</text>

                        <!-- Non-repudiation -->
                        <rect x="500" y="620" width="150" height="50" fill="#FFF3E0" stroke="#FF9800" stroke-width="2"/>
                        <text x="575" y="640" text-anchor="middle" font-size="12" font-weight="bold">NON-REPUDIATION</text>
                        <text x="575" y="655" text-anchor="middle" font-size="10">Cannot deny signing</text>

                        <!-- Efficiency -->
                        <rect x="700" y="620" width="150" height="50" fill="#E1BEE7" stroke="#9C27B0" stroke-width="2"/>
                        <text x="775" y="640" text-anchor="middle" font-size="12" font-weight="bold">EFFICIENCY</text>
                        <text x="775" y="655" text-anchor="middle" font-size="10">Fast verification</text>
                    </g>

                    <!-- Arrow marker definition -->
                    <defs>
                        <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
                        </marker>
                    </defs>
                </svg>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">⚙️</span>How ECDSA Works in Blockchain</h2>

            <div class="example-box">
                <h3>ECDSA Process in Blockchain Transactions</h3>
                <div class="code-snippet">
1. KEY GENERATION:
   Private Key: Random 256-bit number (keep secret)
   Public Key: Private Key × Generator Point on curve
   Address: Hash(Public Key) → Wallet Address

2. TRANSACTION CREATION:
   User creates transaction: {from, to, amount, fee}
   Transaction hash: SHA-256(transaction data)

3. SIGNATURE CREATION:
   Choose random nonce k
   Calculate r = (k × G).x mod n
   Calculate s = k⁻¹(hash + r × private_key) mod n
   Signature = (r, s)

4. TRANSACTION BROADCAST:
   Send {transaction_data, signature, public_key} to network

5. VERIFICATION BY NODES:
   Calculate hash of transaction data
   Verify signature using public key and ECDSA algorithm
   If valid, include in block

6. SECURITY GUARANTEE:
   Only holder of private key could create valid signature
   Any tampering with data invalidates signature
                </div>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">✅</span>Advantages of ECDSA</h2>

            <div class="advantages-box">
                <h3>Key Benefits</h3>
                <ul>
                    <li><strong>Strong Security:</strong> 256-bit ECDSA provides equivalent security to 3072-bit RSA</li>
                    <li><strong>Efficiency:</strong> Smaller key sizes mean faster computations</li>
                    <li><strong>Non-repudiation:</strong> Mathematically proves who signed the transaction</li>
                    <li><strong>Integrity:</strong> Any change to signed data invalidates the signature</li>
                    <li><strong>Scalability:</strong> Fast verification enables high transaction throughput</li>
                    <li><strong>Standardization:</strong> Widely adopted across blockchain platforms</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">❌</span>Limitations and Challenges</h2>

            <div class="disadvantages-box">
                <h3>Technical Limitations</h3>
                <ul>
                    <li><strong>Quantum Vulnerability:</strong> Shor's algorithm could break ECDSA</li>
                    <li><strong>Nonce Reuse:</strong> Using same nonce twice reveals private key</li>
                    <li><strong>Implementation Complexity:</strong> Requires careful implementation</li>
                    <li><strong>Side-channel Attacks:</strong> Timing analysis can leak information</li>
                    <li><strong>Key Management:</strong> Private key loss means permanent loss</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">🚀</span>Blockchain Applications</h2>

            <div class="example-box">
                <h3>Use Cases</h3>
                <ul>
                    <li><strong>Transaction Authorization:</strong> Prove ownership and authorize transfers</li>
                    <li><strong>Smart Contract Execution:</strong> Authenticate contract function calls</li>
                    <li><strong>Multi-signature Wallets:</strong> Require multiple signatures</li>
                    <li><strong>Identity Verification:</strong> Prove identity without revealing private info</li>
                    <li><strong>Message Authentication:</strong> Sign off-chain communications</li>
                    <li><strong>Consensus Participation:</strong> Validate blocks and governance</li>
                </ul>
            </div>
        </div>

    </div>
</body>
</html>