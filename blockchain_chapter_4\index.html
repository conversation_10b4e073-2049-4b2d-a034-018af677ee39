<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blockchain Chapter 4: Cryptocurrency Evolution & Advanced Applications</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #4facfe 100%);
            min-height: 100vh;
            line-height: 1.6;
            overflow-x: hidden;
        }

        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #4facfe 100%);
            animation: gradientShift 30s ease infinite;
        }

        @keyframes gradientShift {
            0% { background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #4facfe 100%); }
            20% { background: linear-gradient(135deg, #f093fb 0%, #4facfe 30%, #667eea 60%, #764ba2 100%); }
            40% { background: linear-gradient(135deg, #4facfe 0%, #667eea 30%, #764ba2 60%, #f093fb 100%); }
            60% { background: linear-gradient(135deg, #764ba2 0%, #f093fb 30%, #4facfe 60%, #667eea 100%); }
            80% { background: linear-gradient(135deg, #667eea 0%, #4facfe 30%, #f093fb 60%, #764ba2 100%); }
            100% { background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #4facfe 100%); }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
            padding: 60px 30px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 30px;
            box-shadow: 0 30px 80px rgba(0,0,0,0.15);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: shimmer 4s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .main-title {
            font-size: 4.5em;
            font-weight: 900;
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb, #4facfe);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 25px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.1);
            position: relative;
            z-index: 1;
        }

        .subtitle {
            font-size: 1.8em;
            color: #555;
            margin-bottom: 35px;
            font-weight: 400;
            position: relative;
            z-index: 1;
        }

        .chapter-info {
            display: flex;
            justify-content: center;
            gap: 40px;
            flex-wrap: wrap;
            margin-top: 35px;
            position: relative;
            z-index: 1;
        }

        .info-badge {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 18px 35px;
            border-radius: 30px;
            font-weight: 700;
            font-size: 1.1em;
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
            transform: translateY(0);
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .info-badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s ease;
        }

        .info-badge:hover::before {
            left: 100%;
        }

        .info-badge:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 50px rgba(102, 126, 234, 0.5);
        }

        .intro-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 50px;
            margin-bottom: 60px;
            box-shadow: 0 25px 70px rgba(0,0,0,0.15);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .intro-section h2 {
            color: #2c3e50;
            font-size: 2.8em;
            margin-bottom: 30px;
            font-weight: 800;
        }

        .intro-section p {
            color: #666;
            font-size: 1.3em;
            margin-bottom: 25px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .feature-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
            padding: 35px;
            border-radius: 20px;
            border-left: 6px solid #667eea;
            box-shadow: 0 18px 45px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            transition: left 0.6s ease;
        }

        .feature-card:hover::before {
            left: 100%;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 30px 70px rgba(0,0,0,0.2);
        }

        .questions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(480px, 1fr));
            gap: 40px;
            margin: 50px 0;
        }

        .question-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 70px rgba(0,0,0,0.15);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            transition: all 0.5s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .question-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.5), transparent);
            transition: left 0.7s ease;
        }

        .question-card:hover::before {
            left: 100%;
        }

        .question-card:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow: 0 40px 90px rgba(0,0,0,0.25);
        }

        .question-number {
            font-size: 4em;
            font-weight: 900;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 25px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .question-title {
            font-size: 1.6em;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 20px;
            line-height: 1.3;
        }

        .question-description {
            color: #666;
            font-size: 1.2em;
            margin-bottom: 30px;
            line-height: 1.7;
        }

        .question-topics {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 25px;
        }

        .topic-tag {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 1em;
            font-weight: 600;
            box-shadow: 0 6px 25px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
        }

        .topic-tag:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
        }

        .difficulty-indicator {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-top: 25px;
        }

        .difficulty-label {
            font-size: 1.1em;
            color: #666;
            font-weight: 600;
        }

        .difficulty-stars {
            display: flex;
            gap: 5px;
        }

        .star {
            width: 16px;
            height: 16px;
            background: #ddd;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .star.filled {
            background: linear-gradient(135deg, #667eea, #764ba2);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .emoji {
            font-size: 2.2em;
            margin-right: 18px;
            filter: drop-shadow(0 3px 6px rgba(0,0,0,0.1));
        }

        .footer {
            text-align: center;
            margin-top: 80px;
            padding: 60px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            box-shadow: 0 25px 70px rgba(0,0,0,0.15);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .footer h3 {
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.2em;
        }

        .footer p {
            color: #666;
            margin-bottom: 18px;
            font-size: 1.2em;
        }

        a {
            text-decoration: none;
            color: inherit;
        }

        @media (max-width: 768px) {
            .main-title {
                font-size: 3.2em;
            }

            .questions-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .chapter-info {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="animated-bg"></div>

    <div class="container">
        <div class="header">
            <h1 class="main-title">🚀 Blockchain Chapter 4</h1>
            <p class="subtitle">Cryptocurrency Evolution & Advanced Applications</p>

            <div class="chapter-info">
                <div class="info-badge">📈 Crypto History</div>
                <div class="info-badge">🔗 Distributed Ledgers</div>
                <div class="info-badge">🤖 Smart Contracts</div>
                <div class="info-badge">🏛️ DAOs</div>
                <div class="info-badge">⚡ Scalability</div>
            </div>
        </div>

        <div class="intro-section">
            <h2>🌟 Advanced Blockchain Applications</h2>
            <p>Welcome to Chapter 4, where we explore the evolution of cryptocurrency, advanced blockchain architectures, and cutting-edge applications. This chapter covers the journey from Bitcoin's inception to modern smart contract platforms and decentralized organizations.</p>

            <div class="features-grid">
                <div class="feature-card">
                    <h3>📚 Historical Evolution</h3>
                    <p>Comprehensive timeline of cryptocurrency development from cypherpunk origins to mainstream adoption</p>
                </div>
                <div class="feature-card">
                    <h3>🔧 Technical Architecture</h3>
                    <p>Deep dive into distributed ledger implementation, mining strategies, and network protocols</p>
                </div>
                <div class="feature-card">
                    <h3>💡 Smart Innovation</h3>
                    <p>Smart contracts, DAOs, and decentralized applications revolutionizing traditional systems</p>
                </div>
                <div class="feature-card">
                    <h3>🔒 Security Analysis</h3>
                    <p>Common vulnerabilities, attack vectors, and security best practices for blockchain applications</p>
                </div>
            </div>
        </div>

        <div class="questions-grid">
            <a href="question_1_cryptocurrency_history.html" class="question-card">
                <div class="question-number">01</div>
                <h3 class="question-title"><span class="emoji">📜</span>Cryptocurrency History & Evolution</h3>
                <p class="question-description">Comprehensive journey from cypherpunk origins to mainstream adoption, covering key milestones, technological breakthroughs, and market evolution.</p>
                <div class="question-topics">
                    <span class="topic-tag">Historical Timeline</span>
                    <span class="topic-tag">Market Evolution</span>
                    <span class="topic-tag">Key Innovations</span>
                </div>
                <div class="difficulty-indicator">
                    <span class="difficulty-label">Difficulty:</span>
                    <div class="difficulty-stars">
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star"></div>
                        <div class="star"></div>
                        <div class="star"></div>
                    </div>
                </div>
            </a>

            <a href="question_2_distributed_ledger.html" class="question-card">
                <div class="question-number">02</div>
                <h3 class="question-title"><span class="emoji">📊</span>Distributed Ledger Implementation</h3>
                <p class="question-description">Technical deep dive into distributed ledger concepts and Bitcoin's implementation, including data structures, consensus, and network protocols.</p>
                <div class="question-topics">
                    <span class="topic-tag">Data Structures</span>
                    <span class="topic-tag">Network Protocol</span>
                    <span class="topic-tag">Bitcoin Core</span>
                </div>
                <div class="difficulty-indicator">
                    <span class="difficulty-label">Difficulty:</span>
                    <div class="difficulty-stars">
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star"></div>
                        <div class="star"></div>
                    </div>
                </div>
            </a>

            <a href="question_3_mining_strategies.html" class="question-card">
                <div class="question-number">03</div>
                <h3 class="question-title"><span class="emoji">⛏️</span>Bitcoin Mining Strategies</h3>
                <p class="question-description">Analysis of mining strategies, pool operations, and their impact on network security, including economic models and centralization concerns.</p>
                <div class="question-topics">
                    <span class="topic-tag">Mining Pools</span>
                    <span class="topic-tag">Security Impact</span>
                    <span class="topic-tag">Economic Models</span>
                </div>
                <div class="difficulty-indicator">
                    <span class="difficulty-label">Difficulty:</span>
                    <div class="difficulty-stars">
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star"></div>
                    </div>
                </div>
            </a>

            <a href="question_4_transaction_fees.html" class="question-card">
                <div class="question-number">04</div>
                <h3 class="question-title"><span class="emoji">💸</span>Transaction Fee Mechanisms</h3>
                <p class="question-description">Comprehensive analysis of fee structures in Bitcoin and Ethereum, including market dynamics, optimization strategies, and user experience.</p>
                <div class="question-topics">
                    <span class="topic-tag">Fee Markets</span>
                    <span class="topic-tag">Gas Optimization</span>
                    <span class="topic-tag">User Experience</span>
                </div>
                <div class="difficulty-indicator">
                    <span class="difficulty-label">Difficulty:</span>
                    <div class="difficulty-stars">
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star"></div>
                        <div class="star"></div>
                    </div>
                </div>
            </a>

            <a href="question_5_ethereum_architecture.html" class="question-card">
                <div class="question-number">05</div>
                <h3 class="question-title"><span class="emoji">🔷</span>Ethereum Blockchain Architecture</h3>
                <p class="question-description">Detailed exploration of Ethereum's architecture, including the EVM, state management, smart contracts, and the transition to Proof of Stake.</p>
                <div class="question-topics">
                    <span class="topic-tag">Ethereum Virtual Machine</span>
                    <span class="topic-tag">State Management</span>
                    <span class="topic-tag">Smart Contracts</span>
                </div>
                <div class="difficulty-indicator">
                    <span class="difficulty-label">Difficulty:</span>
                    <div class="difficulty-stars">
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star"></div>
                    </div>
                </div>
            </a>

            <a href="question_6_dao_organizations.html" class="question-card">
                <div class="question-number">06</div>
                <h3 class="question-title"><span class="emoji">🏛️</span>Decentralized Autonomous Organizations</h3>
                <p class="question-description">Understanding DAOs, their governance mechanisms, real-world implementations, and the challenges of decentralized decision-making.</p>
                <div class="question-topics">
                    <span class="topic-tag">Governance</span>
                    <span class="topic-tag">Voting Mechanisms</span>
                    <span class="topic-tag">Treasury Management</span>
                </div>
                <div class="difficulty-indicator">
                    <span class="difficulty-label">Difficulty:</span>
                    <div class="difficulty-stars">
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star"></div>
                    </div>
                </div>
            </a>

            <a href="question_7_smart_contracts.html" class="question-card">
                <div class="question-number">07</div>
                <h3 class="question-title"><span class="emoji">🤖</span>Smart Contracts & Applications</h3>
                <p class="question-description">Comprehensive guide to smart contracts, their development, deployment, and role in decentralized applications and DeFi protocols.</p>
                <div class="question-topics">
                    <span class="topic-tag">Solidity Programming</span>
                    <span class="topic-tag">DeFi Applications</span>
                    <span class="topic-tag">Contract Security</span>
                </div>
                <div class="difficulty-indicator">
                    <span class="difficulty-label">Difficulty:</span>
                    <div class="difficulty-stars">
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star"></div>
                    </div>
                </div>
            </a>

            <a href="question_8_ghost_protocol.html" class="question-card">
                <div class="question-number">08</div>
                <h3 class="question-title"><span class="emoji">👻</span>GHOST Protocol</h3>
                <p class="question-description">Analysis of the Greedy Heaviest Observed Subtree protocol and its role in improving blockchain efficiency and security.</p>
                <div class="question-topics">
                    <span class="topic-tag">Uncle Blocks</span>
                    <span class="topic-tag">Chain Selection</span>
                    <span class="topic-tag">Network Efficiency</span>
                </div>
                <div class="difficulty-indicator">
                    <span class="difficulty-label">Difficulty:</span>
                    <div class="difficulty-stars">
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                    </div>
                </div>
            </a>

            <a href="question_9_ethereum_vulnerabilities.html" class="question-card">
                <div class="question-number">09</div>
                <h3 class="question-title"><span class="emoji">🔒</span>Ethereum Security Vulnerabilities</h3>
                <p class="question-description">Common security vulnerabilities in Ethereum applications, attack vectors, and best practices for secure smart contract development.</p>
                <div class="question-topics">
                    <span class="topic-tag">Security Audits</span>
                    <span class="topic-tag">Attack Vectors</span>
                    <span class="topic-tag">Best Practices</span>
                </div>
                <div class="difficulty-indicator">
                    <span class="difficulty-label">Difficulty:</span>
                    <div class="difficulty-stars">
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                    </div>
                </div>
            </a>

            <a href="question_10_sidechains.html" class="question-card">
                <div class="question-number">10</div>
                <h3 class="question-title"><span class="emoji">🔗</span>Sidechains & Scalability</h3>
                <p class="question-description">Exploring sidechains as scaling solutions, their implementation, security models, and role in blockchain interoperability.</p>
                <div class="question-topics">
                    <span class="topic-tag">Layer 2 Scaling</span>
                    <span class="topic-tag">Interoperability</span>
                    <span class="topic-tag">Cross-Chain Bridges</span>
                </div>
                <div class="difficulty-indicator">
                    <span class="difficulty-label">Difficulty:</span>
                    <div class="difficulty-stars">
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                        <div class="star filled"></div>
                    </div>
                </div>
            </a>
        </div>

        <div class="footer">
            <h3>🎓 Ready to Explore Advanced Blockchain?</h3>
            <p>Each question includes comprehensive analysis, real-world examples, and technical deep dives.</p>
            <p>Click on any question above to begin your journey into advanced blockchain applications.</p>
            <p><strong>Prerequisites:</strong> Completion of Chapters 1-3 recommended for full understanding</p>
        </div>
    </div>
</body>
</html>
