<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Question 3: Mining Mechanism & Significance</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            line-height: 1.6;
            overflow-x: hidden;
        }

        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            animation: gradientShift 20s ease infinite;
        }

        @keyframes gradientShift {
            0% { background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%); }
            25% { background: linear-gradient(135deg, #f093fb 0%, #667eea 50%, #764ba2 100%); }
            50% { background: linear-gradient(135deg, #764ba2 0%, #f093fb 50%, #667eea 100%); }
            75% { background: linear-gradient(135deg, #667eea 0%, #f093fb 50%, #764ba2 100%); }
            100% { background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%); }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .question-number {
            font-size: 4em;
            font-weight: 900;
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
        }

        .main-title {
            font-size: 2.8em;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .subtitle {
            font-size: 1.3em;
            color: #666;
            margin-bottom: 25px;
        }

        .section {
            margin: 40px 0;
            padding: 35px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 45px rgba(0,0,0,0.1);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .section:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 60px rgba(0,0,0,0.15);
        }

        .section h2 {
            font-size: 2.2em;
            color: #2c3e50;
            margin-bottom: 25px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .emoji {
            font-size: 1.2em;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
        }

        .concept-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 15px;
            padding: 30px;
            margin: 25px 0;
            border-left: 6px solid #2196f3;
            box-shadow: 0 8px 25px rgba(33, 150, 243, 0.1);
        }

        .mining-process {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .process-step {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-top: 4px solid #FF9800;
            position: relative;
            overflow: hidden;
        }

        .process-step::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s ease;
        }

        .process-step:hover::before {
            left: 100%;
        }

        .process-step:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 50px rgba(0,0,0,0.15);
        }

        .step-number {
            font-size: 2em;
            font-weight: 900;
            color: #FF9800;
            margin-bottom: 15px;
        }

        .diagram-container {
            background: rgba(248, 249, 250, 0.95);
            border: 2px solid rgba(222, 226, 230, 0.5);
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
            overflow-x: auto;
            backdrop-filter: blur(10px);
        }

        .highlight {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            padding: 4px 10px;
            border-radius: 6px;
            font-weight: 600;
            color: #856404;
            display: inline-block;
        }

        .mining-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }

        .stat-card {
            background: linear-gradient(135deg, #FF9800, #F57C00);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(255, 152, 0, 0.3);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 35px rgba(255, 152, 0, 0.4);
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: 900;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .incentive-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .incentive-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 6px solid #4CAF50;
        }

        .incentive-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        @media (max-width: 768px) {
            .main-title {
                font-size: 2.2em;
            }

            .question-number {
                font-size: 3em;
            }

            .mining-process {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="animated-bg"></div>

    <div class="container">
        <div class="header">
            <div class="question-number">03</div>
            <h1 class="main-title">⛏️ Mining Mechanism & Significance</h1>
            <p class="subtitle">Proof of Work, Incentives & Network Security</p>
        </div>

        <div class="section">
            <h2><span class="emoji">🔨</span>What is Blockchain Mining?</h2>

            <div class="concept-box">
                <h3>Mining: The Heart of Blockchain Security</h3>
                <p><span class="highlight">Mining</span> is the process by which new transactions are verified and added to the blockchain. Miners compete to solve computationally intensive cryptographic puzzles, with the winner earning the right to add the next block and receive rewards.</p>

                <p><strong>Core Mining Functions:</strong></p>
                <ul>
                    <li><strong>Transaction Validation:</strong> Verify that transactions are legitimate</li>
                    <li><strong>Block Creation:</strong> Package transactions into new blocks</li>
                    <li><strong>Consensus Mechanism:</strong> Ensure network agreement on blockchain state</li>
                    <li><strong>Security Provision:</strong> Make the network resistant to attacks</li>
                    <li><strong>Currency Issuance:</strong> Introduce new cryptocurrency into circulation</li>
                </ul>
            </div>

            <div class="mining-process">
                <div class="process-step">
                    <div class="step-number">1</div>
                    <h3>📝 Transaction Collection</h3>
                    <p>Miners collect pending transactions from the mempool and verify their validity, checking signatures and balances.</p>
                </div>

                <div class="process-step">
                    <div class="step-number">2</div>
                    <h3>📦 Block Assembly</h3>
                    <p>Valid transactions are assembled into a block with a header containing metadata and the previous block hash.</p>
                </div>

                <div class="process-step">
                    <div class="step-number">3</div>
                    <h3>🧮 Proof of Work</h3>
                    <p>Miners compete to find a nonce value that makes the block hash meet the difficulty target (starts with zeros).</p>
                </div>

                <div class="process-step">
                    <div class="step-number">4</div>
                    <h3>📢 Block Broadcast</h3>
                    <p>The winning miner broadcasts the solved block to the network for validation and acceptance by other nodes.</p>
                </div>

                <div class="process-step">
                    <div class="step-number">5</div>
                    <h3>💰 Reward Collection</h3>
                    <p>The successful miner receives the block reward (new coins) plus transaction fees from included transactions.</p>
                </div>

                <div class="process-step">
                    <div class="step-number">6</div>
                    <h3>🔄 Cycle Continues</h3>
                    <p>The process repeats with miners working on the next block, building upon the newly accepted block.</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">🎯</span>Proof of Work Mechanism</h2>

            <div class="concept-box">
                <h3>The Mining Puzzle</h3>
                <p>Miners must find a <span class="highlight">nonce</span> (number used once) that, when combined with the block data and hashed, produces a result with a specific number of leading zeros. This requires enormous computational effort but is easy to verify.</p>

                <p><strong>Example:</strong> Target requires hash starting with 18 zeros</p>
                <ul>
                    <li>Nonce 1,234,567: Hash = 123abc... (invalid)</li>
                    <li>Nonce 9,876,543: Hash = 000000000000000000abc... (valid!)</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">💰</span>Mining Economics & Incentives</h2>

            <div class="mining-stats">
                <div class="stat-card">
                    <div class="stat-number">6.25</div>
                    <div class="stat-label">BTC Block Reward</div>
                </div>

                <div class="stat-card">
                    <div class="stat-number">~$50K</div>
                    <div class="stat-label">Bitcoin Block Value</div>
                </div>

                <div class="stat-card">
                    <div class="stat-number">4 years</div>
                    <div class="stat-label">Halving Cycle</div>
                </div>

                <div class="stat-card">
                    <div class="stat-number">10 min</div>
                    <div class="stat-label">Target Block Time</div>
                </div>
            </div>

            <div class="incentive-grid">
                <div class="incentive-card">
                    <h3>🎁 Block Rewards</h3>
                    <p><strong>New cryptocurrency created with each block</strong></p>
                    <ul>
                        <li>Bitcoin: 6.25 BTC per block (halves every 4 years)</li>
                        <li>Provides economic incentive for miners</li>
                        <li>Controls inflation through predictable issuance</li>
                        <li>Decreases over time to cap total supply</li>
                    </ul>
                </div>

                <div class="incentive-card">
                    <h3>💸 Transaction Fees</h3>
                    <p><strong>Fees paid by users for transaction processing</strong></p>
                    <ul>
                        <li>Variable based on network congestion</li>
                        <li>Higher fees = faster processing priority</li>
                        <li>Becomes more important as block rewards decrease</li>
                        <li>Provides sustainable long-term incentives</li>
                    </ul>
                </div>

                <div class="incentive-card">
                    <h3>🛡️ Network Security</h3>
                    <p><strong>Mining secures the entire blockchain network</strong></p>
                    <ul>
                        <li>Higher hash rate = more security</li>
                        <li>Makes 51% attacks economically unfeasible</li>
                        <li>Protects billions of dollars in value</li>
                        <li>Ensures transaction immutability</li>
                    </ul>
                </div>
            </div>
        </div>

    </div>
</body>
</html>