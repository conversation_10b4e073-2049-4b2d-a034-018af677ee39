<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Question 8: Public vs Private Blockchains</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh; line-height: 1.6; overflow-x: hidden;
        }
        .animated-bg {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: -1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            animation: gradientShift 20s ease infinite;
        }
        @keyframes gradientShift {
            0% { background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%); }
            25% { background: linear-gradient(135deg, #f093fb 0%, #667eea 50%, #764ba2 100%); }
            50% { background: linear-gradient(135deg, #764ba2 0%, #f093fb 50%, #667eea 100%); }
            75% { background: linear-gradient(135deg, #667eea 0%, #f093fb 50%, #764ba2 100%); }
            100% { background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%); }
        }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header {
            text-align: center; margin-bottom: 40px; padding: 40px 20px;
            background: rgba(255, 255, 255, 0.95); border-radius: 25px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1); backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .question-number {
            font-size: 4em; font-weight: 900; margin-bottom: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
            -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;
        }
        .main-title { font-size: 2.8em; font-weight: 700; color: #2c3e50; margin-bottom: 15px; }
        .subtitle { font-size: 1.3em; color: #666; margin-bottom: 25px; }
        .section {
            margin: 40px 0; padding: 35px; background: rgba(255, 255, 255, 0.95);
            border-radius: 20px; box-shadow: 0 15px 45px rgba(0,0,0,0.1);
            backdrop-filter: blur(15px); border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        .section:hover { transform: translateY(-5px); box-shadow: 0 25px 60px rgba(0,0,0,0.15); }
        .section h2 {
            font-size: 2.2em; color: #2c3e50; margin-bottom: 25px; font-weight: 700;
            display: flex; align-items: center; gap: 15px;
        }
        .emoji { font-size: 1.2em; filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1)); }
        .concept-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 15px; padding: 30px; margin: 25px 0;
            border-left: 6px solid #2196f3; box-shadow: 0 8px 25px rgba(33, 150, 243, 0.1);
        }
        .highlight {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            padding: 4px 10px; border-radius: 6px; font-weight: 600;
            color: #856404; display: inline-block;
        }
        .blockchain-types { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 30px; margin: 30px 0; }
        .type-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
            border-radius: 15px; padding: 25px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .type-card:hover { transform: translateY(-8px); box-shadow: 0 20px 50px rgba(0,0,0,0.15); }
        .public-card { border-left: 6px solid #4CAF50; }
        .private-card { border-left: 6px solid #F44336; }
        .consortium-card { border-left: 6px solid #FF9800; }
        .hybrid-card { border-left: 6px solid #9C27B0; }
        .comparison-table {
            width: 100%; border-collapse: collapse; margin: 25px 0;
            background: rgba(255, 255, 255, 0.95); border-radius: 12px; overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .comparison-table th, .comparison-table td {
            padding: 15px; text-align: left; border-bottom: 1px solid rgba(222, 226, 230, 0.5);
        }
        .comparison-table th {
            background: linear-gradient(135deg, #343a40, #495057); color: white;
            font-weight: 700; font-size: 1.1em;
        }
        .comparison-table tr:hover { background: rgba(102, 126, 234, 0.05); }
        @media (max-width: 768px) {
            .main-title { font-size: 2.2em; } .question-number { font-size: 3em; }
            .blockchain-types { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="animated-bg"></div>
    <div class="container">
        <div class="header">
            <div class="question-number">08</div>
            <h1 class="main-title">🔐 Public vs Private Blockchains</h1>
            <p class="subtitle">Deployment Models, Permissions & Enterprise Applications</p>
        </div>

        <div class="section">
            <h2><span class="emoji">🌍</span>Blockchain Deployment Models</h2>
            <div class="concept-box">
                <h3>Understanding Access and Control Models</h3>
                <p>Blockchains can be deployed in different models based on <span class="highlight">access permissions</span> and <span class="highlight">control mechanisms</span>. Each model serves different use cases and offers distinct trade-offs between decentralization, performance, and control.</p>
                
                <p><strong>Key Dimensions:</strong></p>
                <ul>
                    <li><strong>Access Control:</strong> Who can read the blockchain data</li>
                    <li><strong>Participation:</strong> Who can participate in consensus</li>
                    <li><strong>Governance:</strong> Who controls network rules and upgrades</li>
                    <li><strong>Identity:</strong> Whether participants are known or anonymous</li>
                </ul>
            </div>

            <div class="blockchain-types">
                <div class="type-card public-card">
                    <h3>🌐 Public Blockchain</h3>
                    <p><strong>Open, permissionless networks accessible to everyone</strong></p>
                    <ul>
                        <li><strong>Access:</strong> Anyone can read, write, and participate</li>
                        <li><strong>Consensus:</strong> Decentralized (PoW, PoS)</li>
                        <li><strong>Governance:</strong> Community-driven</li>
                        <li><strong>Examples:</strong> Bitcoin, Ethereum, Litecoin</li>
                        <li><strong>Benefits:</strong> Maximum decentralization, censorship resistance</li>
                        <li><strong>Drawbacks:</strong> Slower, energy-intensive, less privacy</li>
                    </ul>
                </div>

                <div class="type-card private-card">
                    <h3>🏢 Private Blockchain</h3>
                    <p><strong>Restricted networks controlled by single organization</strong></p>
                    <ul>
                        <li><strong>Access:</strong> Invitation-only, controlled by owner</li>
                        <li><strong>Consensus:</strong> Centralized or federated</li>
                        <li><strong>Governance:</strong> Single entity control</li>
                        <li><strong>Examples:</strong> JPM Coin, Walmart Food Traceability</li>
                        <li><strong>Benefits:</strong> Fast, efficient, full control, privacy</li>
                        <li><strong>Drawbacks:</strong> Centralized, limited trust benefits</li>
                    </ul>
                </div>

                <div class="type-card consortium-card">
                    <h3>🤝 Consortium Blockchain</h3>
                    <p><strong>Semi-decentralized networks controlled by group</strong></p>
                    <ul>
                        <li><strong>Access:</strong> Restricted to consortium members</li>
                        <li><strong>Consensus:</strong> Controlled by pre-selected nodes</li>
                        <li><strong>Governance:</strong> Shared among consortium</li>
                        <li><strong>Examples:</strong> R3 Corda, Hyperledger Fabric</li>
                        <li><strong>Benefits:</strong> Balanced control, faster than public</li>
                        <li><strong>Drawbacks:</strong> Limited decentralization</li>
                    </ul>
                </div>

                <div class="type-card hybrid-card">
                    <h3>🔀 Hybrid Blockchain</h3>
                    <p><strong>Combines public and private elements</strong></p>
                    <ul>
                        <li><strong>Access:</strong> Selective transparency</li>
                        <li><strong>Consensus:</strong> Mixed public/private validation</li>
                        <li><strong>Governance:</strong> Layered control structure</li>
                        <li><strong>Examples:</strong> IBM Food Trust, VeChain</li>
                        <li><strong>Benefits:</strong> Flexibility, customizable access</li>
                        <li><strong>Drawbacks:</strong> Complex implementation</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">📊</span>Detailed Comparison</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Aspect</th>
                        <th>Public</th>
                        <th>Private</th>
                        <th>Consortium</th>
                        <th>Hybrid</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Decentralization</strong></td>
                        <td>High</td>
                        <td>Low</td>
                        <td>Medium</td>
                        <td>Variable</td>
                    </tr>
                    <tr>
                        <td><strong>Transaction Speed</strong></td>
                        <td>Slow (3-15 TPS)</td>
                        <td>Fast (1000+ TPS)</td>
                        <td>Medium (100-1000 TPS)</td>
                        <td>Variable</td>
                    </tr>
                    <tr>
                        <td><strong>Energy Consumption</strong></td>
                        <td>High</td>
                        <td>Low</td>
                        <td>Low-Medium</td>
                        <td>Variable</td>
                    </tr>
                    <tr>
                        <td><strong>Transparency</strong></td>
                        <td>Full</td>
                        <td>Limited</td>
                        <td>Selective</td>
                        <td>Configurable</td>
                    </tr>
                    <tr>
                        <td><strong>Trust Model</strong></td>
                        <td>Trustless</td>
                        <td>Trusted Authority</td>
                        <td>Multi-party Trust</td>
                        <td>Mixed</td>
                    </tr>
                    <tr>
                        <td><strong>Regulatory Compliance</strong></td>
                        <td>Challenging</td>
                        <td>Easy</td>
                        <td>Moderate</td>
                        <td>Flexible</td>
                    </tr>
                    <tr>
                        <td><strong>Cost</strong></td>
                        <td>High (gas fees)</td>
                        <td>Low</td>
                        <td>Medium</td>
                        <td>Variable</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2><span class="emoji">🎯</span>Use Case Selection</h2>
            <div class="concept-box">
                <h3>Choosing the Right Blockchain Model</h3>
                <p>The choice of blockchain model depends on specific requirements and constraints:</p>
                
                <p><strong>Choose Public Blockchain When:</strong></p>
                <ul>
                    <li>Maximum decentralization and censorship resistance needed</li>
                    <li>Global, trustless interactions required</li>
                    <li>Transparency and auditability are critical</li>
                    <li>Network effects and token economics are important</li>
                    <li><strong>Examples:</strong> Cryptocurrencies, DeFi, NFTs</li>
                </ul>
                
                <p><strong>Choose Private Blockchain When:</strong></p>
                <ul>
                    <li>Single organization needs internal efficiency</li>
                    <li>High performance and low latency required</li>
                    <li>Strict data privacy and control needed</li>
                    <li>Regulatory compliance is critical</li>
                    <li><strong>Examples:</strong> Internal audit trails, supply chain tracking</li>
                </ul>
                
                <p><strong>Choose Consortium When:</strong></p>
                <ul>
                    <li>Multiple organizations need to collaborate</li>
                    <li>Shared control and governance required</li>
                    <li>Industry-wide standards needed</li>
                    <li>Balance between efficiency and decentralization</li>
                    <li><strong>Examples:</strong> Banking consortiums, trade finance</li>
                </ul>
                
                <p><strong>Key Takeaway:</strong> The optimal blockchain model balances decentralization, performance, control, and compliance requirements specific to each use case and organizational context.</p>
            </div>
        </div>
    </div>
</body>
</html>
