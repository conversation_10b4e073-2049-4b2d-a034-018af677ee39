<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Question 4: <PERSON><PERSON><PERSON></title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh; line-height: 1.6; overflow-x: hidden;
        }
        .animated-bg {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: -1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            animation: gradientShift 20s ease infinite;
        }
        @keyframes gradientShift {
            0% { background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%); }
            25% { background: linear-gradient(135deg, #f093fb 0%, #667eea 50%, #764ba2 100%); }
            50% { background: linear-gradient(135deg, #764ba2 0%, #f093fb 50%, #667eea 100%); }
            75% { background: linear-gradient(135deg, #667eea 0%, #f093fb 50%, #764ba2 100%); }
            100% { background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%); }
        }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header {
            text-align: center; margin-bottom: 40px; padding: 40px 20px;
            background: rgba(255, 255, 255, 0.95); border-radius: 25px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1); backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .question-number {
            font-size: 4em; font-weight: 900; margin-bottom: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
            -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;
        }
        .main-title { font-size: 2.8em; font-weight: 700; color: #2c3e50; margin-bottom: 15px; }
        .subtitle { font-size: 1.3em; color: #666; margin-bottom: 25px; }
        .section {
            margin: 40px 0; padding: 35px; background: rgba(255, 255, 255, 0.95);
            border-radius: 20px; box-shadow: 0 15px 45px rgba(0,0,0,0.1);
            backdrop-filter: blur(15px); border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        .section:hover { transform: translateY(-5px); box-shadow: 0 25px 60px rgba(0,0,0,0.15); }
        .section h2 {
            font-size: 2.2em; color: #2c3e50; margin-bottom: 25px; font-weight: 700;
            display: flex; align-items: center; gap: 15px;
        }
        .emoji { font-size: 1.2em; filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1)); }
        .concept-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 15px; padding: 30px; margin: 25px 0;
            border-left: 6px solid #2196f3; box-shadow: 0 8px 25px rgba(33, 150, 243, 0.1);
        }
        .highlight {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            padding: 4px 10px; border-radius: 6px; font-weight: 600;
            color: #856404; display: inline-block;
        }
        .tree-comparison { display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin: 30px 0; }
        .tree-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
            border-radius: 15px; padding: 25px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease; border-top: 4px solid #667eea;
        }
        .tree-card:hover { transform: translateY(-8px); box-shadow: 0 20px 50px rgba(0,0,0,0.15); }
        .diagram-container {
            background: rgba(248, 249, 250, 0.95); border: 2px solid rgba(222, 226, 230, 0.5);
            border-radius: 15px; padding: 25px; margin: 25px 0; overflow-x: auto; backdrop-filter: blur(10px);
        }
        @media (max-width: 768px) {
            .main-title { font-size: 2.2em; } .question-number { font-size: 3em; }
            .tree-comparison { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="animated-bg"></div>
    <div class="container">
        <div class="header">
            <div class="question-number">04</div>
            <h1 class="main-title">🌳 Merkle Patricia Tree</h1>
            <p class="subtitle">Advanced Data Structure for Blockchain State Management</p>
        </div>

        <div class="section">
            <h2><span class="emoji">🏗️</span>Understanding Merkle Patricia Trees</h2>
            <div class="concept-box">
                <h3>Hybrid Data Structure</h3>
                <p>A <span class="highlight">Merkle Patricia Tree</span> combines the benefits of Merkle trees (cryptographic verification) with Patricia tries (efficient key-value storage). It's the core data structure used by Ethereum to store account states, contract storage, and transaction receipts.</p>
                
                <p><strong>Key Components:</strong></p>
                <ul>
                    <li><strong>Merkle Tree:</strong> Provides cryptographic integrity verification</li>
                    <li><strong>Patricia Trie:</strong> Enables efficient key-value storage and retrieval</li>
                    <li><strong>Radix Tree:</strong> Compresses paths to reduce storage overhead</li>
                    <li><strong>Hash Pointers:</strong> Link nodes using cryptographic hashes</li>
                </ul>
            </div>

            <div class="tree-comparison">
                <div class="tree-card">
                    <h3>🌲 Merkle Tree Benefits</h3>
                    <ul>
                        <li>Cryptographic integrity verification</li>
                        <li>Efficient proof of inclusion</li>
                        <li>Tamper detection capabilities</li>
                        <li>Logarithmic verification time</li>
                        <li>Widely used in blockchain systems</li>
                    </ul>
                </div>

                <div class="tree-card">
                    <h3>🔍 Patricia Trie Benefits</h3>
                    <ul>
                        <li>Efficient key-value storage</li>
                        <li>Path compression optimization</li>
                        <li>Fast insertion and deletion</li>
                        <li>Memory-efficient representation</li>
                        <li>Supports variable-length keys</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">🔧</span>Structure & Implementation</h2>
            <div class="diagram-container">
                <svg width="100%" height="500" viewBox="0 0 1200 500">
                    <text x="600" y="30" text-anchor="middle" font-size="18" font-weight="bold">MERKLE PATRICIA TREE STRUCTURE</text>
                    
                    <!-- Root Node -->
                    <rect x="550" y="60" width="100" height="50" fill="#4CAF50" stroke="#2E7D32" stroke-width="2" rx="5"/>
                    <text x="600" y="80" text-anchor="middle" font-size="12" font-weight="bold" fill="white">ROOT</text>
                    <text x="600" y="95" text-anchor="middle" font-size="10" fill="white">Hash: 0x1a2b</text>
                    
                    <!-- Branch Nodes -->
                    <rect x="300" y="150" width="80" height="40" fill="#2196F3" stroke="#1976D2" stroke-width="2" rx="5"/>
                    <text x="340" y="170" text-anchor="middle" font-size="11" font-weight="bold" fill="white">BRANCH</text>
                    <text x="340" y="185" text-anchor="middle" font-size="9" fill="white">0x3c4d</text>
                    
                    <rect x="500" y="150" width="80" height="40" fill="#2196F3" stroke="#1976D2" stroke-width="2" rx="5"/>
                    <text x="540" y="170" text-anchor="middle" font-size="11" font-weight="bold" fill="white">BRANCH</text>
                    <text x="540" y="185" text-anchor="middle" font-size="9" fill="white">0x5e6f</text>
                    
                    <rect x="700" y="150" width="80" height="40" fill="#2196F3" stroke="#1976D2" stroke-width="2" rx="5"/>
                    <text x="740" y="170" text-anchor="middle" font-size="11" font-weight="bold" fill="white">BRANCH</text>
                    <text x="740" y="185" text-anchor="middle" font-size="9" fill="white">0x7g8h</text>
                    
                    <!-- Leaf Nodes -->
                    <rect x="200" y="250" width="100" height="40" fill="#FF9800" stroke="#F57C00" stroke-width="2" rx="5"/>
                    <text x="250" y="270" text-anchor="middle" font-size="11" font-weight="bold" fill="white">LEAF</text>
                    <text x="250" y="285" text-anchor="middle" font-size="9" fill="white">Account A</text>
                    
                    <rect x="350" y="250" width="100" height="40" fill="#FF9800" stroke="#F57C00" stroke-width="2" rx="5"/>
                    <text x="400" y="270" text-anchor="middle" font-size="11" font-weight="bold" fill="white">LEAF</text>
                    <text x="400" y="285" text-anchor="middle" font-size="9" fill="white">Account B</text>
                    
                    <rect x="500" y="250" width="100" height="40" fill="#FF9800" stroke="#F57C00" stroke-width="2" rx="5"/>
                    <text x="550" y="270" text-anchor="middle" font-size="11" font-weight="bold" fill="white">LEAF</text>
                    <text x="550" y="285" text-anchor="middle" font-size="9" fill="white">Contract X</text>
                    
                    <rect x="650" y="250" width="100" height="40" fill="#FF9800" stroke="#F57C00" stroke-width="2" rx="5"/>
                    <text x="700" y="270" text-anchor="middle" font-size="11" font-weight="bold" fill="white">LEAF</text>
                    <text x="700" y="285" text-anchor="middle" font-size="9" fill="white">Contract Y</text>
                    
                    <!-- Connections -->
                    <line x1="580" y1="110" x2="360" y2="150" stroke="#333" stroke-width="2"/>
                    <line x1="600" y1="110" x2="540" y2="150" stroke="#333" stroke-width="2"/>
                    <line x1="620" y1="110" x2="720" y2="150" stroke="#333" stroke-width="2"/>
                    
                    <line x1="320" y1="190" x2="250" y2="250" stroke="#333" stroke-width="2"/>
                    <line x1="360" y1="190" x2="400" y2="250" stroke="#333" stroke-width="2"/>
                    <line x1="540" y1="190" x2="550" y2="250" stroke="#333" stroke-width="2"/>
                    <line x1="740" y1="190" x2="700" y2="250" stroke="#333" stroke-width="2"/>
                    
                    <!-- Data Examples -->
                    <text x="250" y="320" text-anchor="middle" font-size="10">Balance: 100 ETH</text>
                    <text x="250" y="335" text-anchor="middle" font-size="10">Nonce: 5</text>
                    
                    <text x="400" y="320" text-anchor="middle" font-size="10">Balance: 50 ETH</text>
                    <text x="400" y="335" text-anchor="middle" font-size="10">Nonce: 2</text>
                    
                    <text x="550" y="320" text-anchor="middle" font-size="10">Code Hash</text>
                    <text x="550" y="335" text-anchor="middle" font-size="10">Storage Root</text>
                    
                    <text x="700" y="320" text-anchor="middle" font-size="10">Code Hash</text>
                    <text x="700" y="335" text-anchor="middle" font-size="10">Storage Root</text>
                    
                    <!-- Key Features -->
                    <text x="100" y="400" font-size="12" font-weight="bold">Key Features:</text>
                    <text x="100" y="420" font-size="11">• Cryptographic integrity through hash pointers</text>
                    <text x="100" y="435" font-size="11">• Efficient storage with path compression</text>
                    <text x="100" y="450" font-size="11">• Fast lookups and updates</text>
                    <text x="100" y="465" font-size="11">• Supports proofs of inclusion/exclusion</text>
                    
                    <text x="600" y="400" font-size="12" font-weight="bold">Ethereum Usage:</text>
                    <text x="600" y="420" font-size="11">• State trie: Account balances and nonces</text>
                    <text x="600" y="435" font-size="11">• Storage trie: Smart contract storage</text>
                    <text x="600" y="450" font-size="11">• Transaction trie: Block transactions</text>
                    <text x="600" y="465" font-size="11">• Receipt trie: Transaction receipts</text>
                </svg>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">⚡</span>Ethereum Implementation</h2>
            <div class="concept-box">
                <h3>State Management in Ethereum</h3>
                <p>Ethereum uses Merkle Patricia Trees for four critical data structures:</p>
                <ul>
                    <li><strong>State Trie:</strong> Maps addresses to account states (balance, nonce, code hash, storage root)</li>
                    <li><strong>Storage Trie:</strong> Stores smart contract state variables</li>
                    <li><strong>Transaction Trie:</strong> Contains all transactions in a block</li>
                    <li><strong>Receipt Trie:</strong> Stores transaction execution results and logs</li>
                </ul>
                
                <p><strong>Benefits for Blockchain:</strong></p>
                <ul>
                    <li>Enables light clients to verify specific data without downloading entire blockchain</li>
                    <li>Provides cryptographic proofs for state transitions</li>
                    <li>Allows efficient state synchronization between nodes</li>
                    <li>Supports rollback and state history management</li>
                </ul>
                
                <p><strong>Key Takeaway:</strong> Merkle Patricia Trees enable Ethereum to efficiently manage complex state while maintaining cryptographic integrity and supporting light client verification.</p>
            </div>
        </div>
    </div>
</body>
</html>
